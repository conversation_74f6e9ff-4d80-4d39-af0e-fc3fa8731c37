/*
 * ProjectName：UaApp
 * FilePath：lib/routes/app_routes.dart
 * FileName：app_routes
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/16 18:41:19
 */
abstract class Routes {
  static const splash = '/';
  static const webView = '/webView';
  static const login = '/login';
  static const merchantSelect = '/merchantSelect';
  static const register = '/register';
  static const main = '/main';
  static const home = '/home';
  static const mine = '/mine';
  static const transaction = '/transaction';
  static String transactionDetail = '/transactionDetail/:transactionId';
  static String transactionRefund = '/transactionRefund';
  static String transactionRefundResult = '/transactionRefundResult';
  static const settlement = '/settlement';
  static const setting = '/setting';
  static const settingLanguage = '/settingLanguage';
  static const closeAccount = '/closeAccount';

}
