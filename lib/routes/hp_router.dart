/*
 * ProjectName：UaApp
 * FilePath：lib/routes/hp_router.dart
 * FileName：hp_router
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/16 18:41:19
 */
import 'package:auapp/pages/main/main_controller.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class HPRouter {
  /// 重置跟视图
  static void resolvePage(
    String newRouteName, {
    RoutePredicate? predicate,
    dynamic arguments,
    int? id,
    Map<String, String>? parameters,
    bool preventDuplicates = true,
    bool destroyBinding = false,
  }) {
    if (preventDuplicates && isCurrentPage(newRouteName)) {
      return;
    }
    // 是否销毁tab 的所有 binding
    if (destroyBinding && Get.isRegistered<MainController>()) {
      final MainController mainController = Get.find<MainController>();
      mainController.destroyBinding();
    }
    // 或者当路由是登录页面的时候要强制清掉main
    if (!destroyBinding &&
        newRouteName == Routes.login &&
        Get.isRegistered<MainController>()) {
      final MainController mainController = Get.find<MainController>();
      mainController.destroyBinding();
    }

    Get.offAllNamed(newRouteName,
        predicate: predicate,
        arguments: arguments,
        id: id,
        parameters: parameters);
  }

  /// push 跳转下一页
  static void pushPage(
    String page, {
    dynamic arguments,
    int? id,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
  }) {
    Get.toNamed(page,
        arguments: arguments,
        id: id,
        preventDuplicates: preventDuplicates,
        parameters: parameters);
  }

  /// pop 回退至上一页
  static void popBack<T>({
    T? result,
    bool closeOverlays = false,
    bool canPop = true,
    int? id,
  }) {
    Get.back(
      result: result,
      closeOverlays: closeOverlays,
      canPop: canPop,
      id: id,
    );
  }

  /// pop 回退至指定页
  static void popToPage(
    String page, {
    dynamic arguments,
    int? id,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
  }) {
    Get.offNamed(page,
        arguments: arguments,
        id: id,
        preventDuplicates: preventDuplicates,
        parameters: parameters);
  }

  // 判断当前路由是否处于某个页面
  static bool isCurrentPage(String page) {
    return Get.currentRoute == page;
  }
}
