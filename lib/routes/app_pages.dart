/*
 * ProjectName：UaApp
 * FilePath：lib/routes/app_pages.dart
 * FileName：app_pages
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/16 18:41:19
 */
import 'package:auapp/pages/home/<USER>';
import 'package:auapp/pages/home/<USER>';
import 'package:auapp/pages/merchantSelect/merchant_select_binding.dart';
import 'package:auapp/pages/merchantSelect/merchant_select_page.dart';
import 'package:auapp/pages/mine/mine_main_binding.dart';
import 'package:auapp/pages/mine/mine_main_page.dart';
import 'package:auapp/pages/setting/closeAccount/close_account_binding.dart';
import 'package:auapp/pages/setting/closeAccount/close_account_page.dart';
import 'package:auapp/pages/setting/closeAccount/close_account_status_binding.dart';
import 'package:auapp/pages/setting/closeAccount/close_account_status_page.dart';
import 'package:auapp/pages/setting/language/language_setting_binding.dart';
import 'package:auapp/pages/setting/language/language_setting_page.dart';
import 'package:auapp/pages/setting/setting_binding.dart';
import 'package:auapp/pages/setting/setting_page.dart';
import 'package:auapp/pages/setting/validate/validate_phone_binding.dart';
import 'package:auapp/pages/setting/validate/validate_phone_page.dart';
import 'package:auapp/pages/transaction/detail/transaction_detail_binding.dart';
import 'package:auapp/pages/transaction/detail/transaction_detail_page.dart';
import 'package:auapp/pages/transaction/refund/transaction_refund_binding.dart';
import 'package:auapp/pages/transaction/refund/transaction_refund_page.dart';
import 'package:auapp/pages/transaction/refundResult/refund_result_binding.dart';
import 'package:auapp/pages/transaction/refundResult/refund_result_page.dart';
import 'package:auapp/pages/transaction/transaction_main_binding.dart';
import 'package:auapp/pages/transaction/transaction_main_page.dart';
import 'package:auapp/pages/webView/web_view_binding.dart';
import 'package:auapp/pages/webView/web_view_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';
import 'package:auapp/pages/login/login_binding.dart';
import 'package:auapp/pages/launch/splash_page.dart';
import 'package:auapp/pages/login/login_page.dart';
import 'package:auapp/pages/main/main_binding.dart';
import 'package:auapp/pages/main/main_page.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart';
import 'package:auapp/pages/settlement/settlement_binding.dart';
import 'package:auapp/pages/settlement/settlement_page.dart';

import 'package:auapp/pages/launch/splash_binding.dart';
import 'app_routes.dart';

class AppPages {
  static const initial = Routes.splash;

  static final routes = [
    GetPage(
      name: Routes.splash,
      page: () => const SplashPage(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: Routes.webView,
      page: () => const HPWebViewPage(),
      binding: HPWebViewBinding(),
    ),
    GetPage(
      name: Routes.login,
      page: () => LoginPage(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: Routes.merchantSelect,
      page: () => const MerchantSelectPage(),
      binding: MerchantSelectBinding(),
    ),
    GetPage(
      name: Routes.main,
      page: () => const MainPage(),
      binding: MainBinding(),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.home,
      page: () => HomeMainPage(),
      binding: HomeMainBinding(),
    ),
    GetPage(
      name: Routes.mine,
      page: () => MineMainPage(),
      binding: MineMainBinding(),
    ),
    GetPage(
      name: Routes.transaction,
      page: () => TransactionMainPage(),
      binding: TransactionMainBinding(),
    ),
    GetPage(
      name: Routes.transactionDetail,
      page: () => const TransactionDetailPage(),
      binding: TransactionDetailBinding(),
    ),
    GetPage(
      name: Routes.settlement,
      page: () => const SettlementPage(),
      binding: SettlementBinding(),
    ),
    GetPage(
      name: Routes.transactionRefund,
      page: () => const TransactionRefundPage(),
      binding: TransactionRefundBinding(),
    ),
    GetPage(
      name: Routes.transactionRefundResult,
      page: () => const RefundResultPage(),
      binding: RefundResultBinding(),
    ),
    GetPage(
      name: Routes.setting,
      page: () => SettingPage(),
      binding: SettingBinding(),
    ),
    GetPage(
      name: Routes.settingLanguage,
      page: () => const LanguageSettingPage(),
      binding: LanguageSettingBinding(),
    ),
    GetPage(
      name: Routes.closeAccount,
      page: () => const CloseAccountPage(),
      binding: CloseAccountBinding(),
    ),
    GetPage(
      name: Routes.closeAccountResult,
      page: () => const CloseAccountStatusPage(),
      binding: CloseAccountStatusBinding(),
    ),
    GetPage(
      name: Routes.securityValidate,
      page: () => const ValidatePhonePage(),
      binding: ValidatePhoneBinding(),
    ),
  ];
}
