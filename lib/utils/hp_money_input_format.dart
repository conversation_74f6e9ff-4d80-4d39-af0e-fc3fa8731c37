/*
 * ProjectName：UaApp
 * FilePath：lib/utils
 * FileName：hp_money_input_format
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 自定义金额输入格式化器
 * @date 2025/5/27 11:18:26
 */

import 'package:flutter/services.dart';
class HpMoneyInputFormat extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue,
      TextEditingValue newValue,
      ) {
    String newText = newValue.text;

    // 1. 空值直接放行
    if (newText.isEmpty) return newValue;

    // 2. 禁止以小数点开头（自动补零）
    if (newText.startsWith('.')) {
      return const TextEditingValue(
        text: '0.',
        selection: TextSelection.collapsed(offset: 2),
      );
    }

    // 3. 禁止输入多个小数点
    if (newText.split('.').length > 2) {
      return oldValue;
    }

    // 4. 限制小数点后最多两位
    if (newText.contains('.')) {
      final parts = newText.split('.');
      if (parts[1].length > 2) {
        return oldValue;
      }
    }

    // 5. 修复前导零处理（关键修复点）
    if (newText.startsWith('0') && newText.length > 1 && !newText.startsWith('0.')) {
      // 计算删除前导零后的新文本
      final cleanedText = newText.replaceFirst(RegExp(r'^0+'), '');
      // 计算光标位置偏移量（原光标位置 - 删除的前导零数量）
      final offsetShift = newText.length - cleanedText.length;
      final newOffset = (newValue.selection.end - offsetShift).clamp(0, cleanedText.length);

      return TextEditingValue(
        text: cleanedText,
        selection: TextSelection.collapsed(offset: newOffset),
      );
    }

    return newValue;
  }
}