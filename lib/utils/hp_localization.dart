import 'dart:ui';

import 'package:auapp/lang/translation_service.dart';
import 'package:auapp/pub/constants/storage_constants.dart';
import 'package:auapp/services/storage_service.dart';
import 'package:get/get.dart';

class HPLocalization {
  static void localize(String value) {
    switch (value) {
      case 'en-US':
        Get.updateLocale(const Locale('en', 'US'));
        break;
      case 'zh-CN':
        Get.updateLocale(const Locale('zh', 'CN'));
        break;
      case 'zh-HK':
        Get.updateLocale(const Locale('zh', 'HK'));
        break;
      default:
        Get.updateLocale(const Locale('en', 'US'));
        break;
    }
    print('设置国际化语言为：$value');
    StorageService.setString(StorageConstants.huepayLanguage, value);
  }

  static String getLocale() {
    return StorageService.getString(StorageConstants.huepayLanguage) ?? "en-US";
  }
}
