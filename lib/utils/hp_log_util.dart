import 'dart:developer';
import 'package:flutter/foundation.dart';

class HPLogUtil {
  static const String _defTag = 'Huepay';
  static const bool _debugMode = kDebugMode; //是否是debug模式,true: log v 不输出.
  static const int _maxLen = 128;

  static void d(Object? object) {
    if (_debugMode) {
      log('$_defTag d | ${object?.toString()}');
    }
  }

  static void e(Object? object) {
    _printLog(_defTag, ' e ', object);
  }

  static void v(Object? object) {
    if (_debugMode) {
      _printLog(_defTag, ' v ', object);
    }
  }

  static void _printLog(String? tag, String stag, Object? object) {
    String da = object?.toString() ?? 'null';
    tag = tag ?? _defTag;
    if (da.length <= _maxLen) {
      print('$tag$stag $da');
      return;
    }
    print(
        '$tag$stag — — — — — — — — — — — — — — — — st — — — — — — — — — — — — — — — —');
    while (da.isNotEmpty) {
      if (da.length > _maxLen) {
        print('$tag$stag| ${da.substring(0, _maxLen)}');
        da = da.substring(_maxLen, da.length);
      } else {
        print('$tag$stag| $da');
        da = '';
      }
    }
    print(
        '$tag$stag — — — — — — — — — — — — — — — — ed — — — — — — — — — — — — — — — —');
  }
}
