/*
 * ProjectName：UaApp
 * FilePath：lib/utils
 * FileName：hp_regex
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/16 18:23:37
 */
class HPRegex {
  /// 验证邮箱
  static bool isEmail(String email) {
    RegExp regExp = RegExp(r'^[a-zA-Z0-9_.+-]+@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$');
    return regExp.hasMatch(email);
  }

  /// 验证大陆地区手机号
  static bool isMobile(String mobile) {
    RegExp regExp = RegExp(r'^1[3-9]\d{9}$');
    return regExp.hasMatch(mobile);
  }
}
