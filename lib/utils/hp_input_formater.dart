/*
 * ProjectName：UaApp
 * FilePath：lib/utils
 * FileName：hp_input_formater
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/23 11:18:34
 */

import 'package:flutter/services.dart';

class HPInputFormatter {
  static TextInputFormatter inputNumberOrLetterFormatter =
      FilteringTextInputFormatter.allow(RegExp("[0-9a-zA-Z]"));
  static TextInputFormatter inputNumberFormatter =
      FilteringTextInputFormatter.allow(RegExp("[0-9]"));
  static TextInputFormatter inputDoubleFormatter =
      FilteringTextInputFormatter.allow(RegExp("[0-9.]"));
  static TextInputFormatter inputMoneyFormatter =
  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'));
  static TextInputFormatter inputIDNumberFormatter =
      FilteringTextInputFormatter.allow(RegExp("[0-9Xx]"));
  static TextInputFormatter inputNumberOrCapitalLetterFormatter =
      FilteringTextInputFormatter.allow(RegExp("[0-9A-Z]")); //大写字母和数字

  static TextInputFormatter loginPasswordFormatter =
  FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9!@#$%^&*()_]')); //

  /// 限制汉字和字母输入
  static TextInputFormatter denyChineseAndEmojiFormatter = FilteringTextInputFormatter.deny(
    RegExp(
      r'[\u4e00-\u9fff]|'          // 汉字
      r'[\u{1F600}-\u{1F64F}]|'   // 😀-🙏
      r'[\u{2600}-\u{26FF}]|'     // ☀-⛿
      r'[\u{1F300}-\u{1F5FF}]|'     // 杂项符号和象形文字
      r'[\u{1F600}-\u{1F64F}]|'    // 表情符号（笑脸和人物）
      r'[\u{1F680}-\u{1F6FF}]|'    // 交通和地图符号
      r'[\u{1F700}-\u{1F77F}]|'    // 炼金术符号
      r'[\u{1F900}-\u{1F9FF}]|'    // 补充符号和象形文字
      r'[\u{2600}-\u{26FF}]|'      // 杂项符号
      r'[\u{2700}-\u{27BF}]|'      // 装饰符号
      r'[\u{2B05}-\u{2B07}]|'      // 箭头符号
      r'[\u{2B1B}-\u{2B1C}]|'      // 方形符号
      r'[\u{2B50}-\u{2B55}]|'      // 星形符号
      r'[\u{1F1E6}-\u{1F1FF}]'     // 地区指示符号（国旗）
      r'[\u{2700}-\u{27BF}]',     // ✀-➿
      unicode: true,
    ),
  );

}
