/*
 * ProjectName：UaApp
 * FilePath：lib/utils
 * FileName：hp_encrypt
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/20 11:27:41
 */

import 'dart:convert';

import 'package:crypto/crypto.dart';

class HPEncrypt {
  static Future<String> getSha256String(String str) async {
    var digest = await sha256.bind(Utf8Encoder().bind(Stream.value(str))).first;
    return digest.toString();
  }
}
