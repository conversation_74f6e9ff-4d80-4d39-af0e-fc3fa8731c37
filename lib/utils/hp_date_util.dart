import 'package:auapp/utils/hp_localization.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HPDateUtil {
  /*
  * 每个月对应的天数
  * */
  static const List<int> _daysInMonth = <int>[
    31,
    -1,
    31,
    30,
    31,
    30,
    31,
    31,
    30,
    31,
    30,
    31
  ];

  /*
  * 根据年月获取月的天数
  * */
  static int getDaysInMonth(int year, int month) {
    if (month == DateTime.february) {
      final bool isLeapYear =
          (year % 4 == 0) && (year % 100 != 0) || (year % 400 == 0);
      if (isLeapYear) return 29;
      return 28;
    }
    return _daysInMonth[month - 1];
  }

  /*
  * 得到这个月的第一天是星期几（0 是 星期日 1 是 星期一...）
  * */
  static int computeFirstDayOffset(
      int year, int month, MaterialLocalizations localizations) {
    // 0-based day of week, with 0 representing Monday.
    final int weekdayFromMonday = DateTime(year, month).weekday - 1;
    // 0-based day of week, with 0 representing Sunday.
    final int firstDayOfWeekFromSunday = localizations.firstDayOfWeekIndex;
    // firstDayOfWeekFromSunday recomputed to be Monday-based
    final int firstDayOfWeekFromMonday = (firstDayOfWeekFromSunday - 1) % 7;
    // Number of days between the first day of week appearing on the calendar,
    // and the day corresponding to the 1-st of the month.
    return (weekdayFromMonday - firstDayOfWeekFromMonday) % 7;
  }

  /// 获取天
  static List getDay(int year, int month, MaterialLocalizations localizations) {
    List labels = [];
    final int daysInMonth = getDaysInMonth(year, month);
    final int firstDayOffset =
        computeFirstDayOffset(year, month, localizations);
    for (int i = 0; true; i += 1) {
      // 1-based day of month, e.g. 1-31 for January, and 1-29 for February on
      // a leap year.
      final int day = i - firstDayOffset + 1;
      if (day > daysInMonth) break;
      if (day < 1) {
        labels.add(0);
      } else {
        labels.add(DateTime(year, month, day));
      }
    }
    return labels;
  }

  /*
  * 每个月前面空出来的天数
  * */
  static int numberOfHeadPlaceholderForMonth(
      int year, int month, MaterialLocalizations localizations) {
    return computeFirstDayOffset(year, month, localizations);
  }

  /*
  * 根据当前年月份计算当前月份显示几行
  * */
  static int getRowsForMonthYear(
      int year, int month, MaterialLocalizations localizations) {
    int currentMonthDays = getDaysInMonth(year, month);
    // 每个月前面空出来的天数
    int placeholderDays =
        numberOfHeadPlaceholderForMonth(year, month, localizations);
    int rows = (currentMonthDays + placeholderDays) ~/ 7; // 向下取整
    int remainder = (currentMonthDays + placeholderDays) % 7; // 取余（最后一行的天数）
    if (remainder > 0) {
      rows += 1;
    }
    return rows;
  }

  /*
  * 根据当前年月份计算每个月后面空出来的天数
  * */
  static int getLastRowDaysForMonthYear(
      int year, int month, MaterialLocalizations localizations) {
    int count = 0;
    // 当前月份的天数
    int currentMonthDays = getDaysInMonth(year, month);
    // 每个月前面空出来的天数
    int placeholderDays =
        numberOfHeadPlaceholderForMonth(year, month, localizations);
    int remainder = (currentMonthDays + placeholderDays) % 7; // 取余（最后一行的天数）
    if (remainder > 0) {
      count = 7 - remainder;
    }
    return count;
  }

  /*
   * 根据传入的date返回map日期
   * 比如20200210，返回{'year':'2020','month':'02','day':'10','date':'20200210'}
   */
  static Map<String, String> getYearMonthDay(String? date,
      {bool wrapZero = false}) {
    if (TextUtil.isEmpty(date)) {
      return {};
    }
    Map<String, String> dateParams = {};
    dateParams['year'] = date!.substring(0, 4);
    var month = date.substring(4, 6);
    if (wrapZero && month.startsWith('0')) {
      month = month.replaceFirst('0', '');
    }
    dateParams['month'] = month;
    var day = date.substring(6, 8);
    if (wrapZero && day.startsWith('0')) {
      day = day.replaceFirst('0', '');
    }
    dateParams['day'] = day;
    dateParams['date'] = date;
    return dateParams;
  }

  /*
   * 根据传入的date返回map日期
   * 比如20200210，返回{'year':'2020','month':'02','day':'10','date':'20200210', 'week': '周一'}
   */
  static Map<String, String> getDateMap(DateTime date,
      {bool wrapZero = false}) {
    // 时间格式化为 yyyyMMdd
    String dateStr = HPDateUtil.dateFormat(date, format: 'yyyyMMdd');
    Map<String, String> dateParams =
        getYearMonthDay(dateStr, wrapZero: wrapZero);
    dateParams['week'] = getZHWeekDay(date);
    return dateParams;
  }

  /*
   * 获取今天周几
   */
  static String getZHWeekDay(DateTime? dateTime) {
    if (dateTime == null) return "";
    String weekday = "";
    switch (dateTime.weekday) {
      case 1:
        weekday = "transaction_mon".tr;
        break;
      case 2:
        weekday = "transaction_tue".tr;
        break;
      case 3:
        weekday = "transaction_wed".tr;
        break;
      case 4:
        weekday = "transaction_thu".tr;
        break;
      case 5:
        weekday = "transaction_fri".tr;
        break;
      case 6:
        weekday = "transaction_sat".tr;
        break;
      case 7:
        weekday = "transaction_sun".tr;
        break;
      default:
        break;
    }
    return weekday;
  }

  /*
   * 获取今天是几月
   */
  static String getZHMonthDay(DateTime? dateTime) {
    if (dateTime == null) return "";
    String month = "";
    switch (dateTime.month) {
      case 1:
        month = "transaction_january".tr;
        break;
      case 2:
        month = "transaction_february".tr;
        break;
      case 3:
        month = "transaction_march".tr;
        break;
      case 4:
        month = "transaction_april".tr;
        break;
      case 5:
        month = "transaction_may".tr;
        break;
      case 6:
        month = "transaction_june".tr;
        break;
      case 7:
        month = "transaction_july".tr;
        break;
      case 8:
        month = "transaction_august".tr;
        break;
      case 9:
        month = "transaction_september".tr;
        break;
      case 10:
        month = "transaction_october".tr;
        break;
      case 11:
        month = "transaction_november".tr;
        break;
      case 12:
        month = "transaction_december".tr;
        break;
      default:
        break;
    }
    return month;
  }

  static String addZero(int days) {
    if (days < 10) {
      return '0$days';
    }
    return '$days';
  }

  /// formatted dateStr(eg: yyyyMMdd)
  static String lastDayOfDate(String dateStr) {
    DateTime lastDate = DateTime.parse(dateStr).add(const Duration(days: -1));
    String lastDateStr = DateUtil.formatDate(lastDate, format: "yyyyMMdd");

    return lastDateStr;
  }

  /// formatted dateStr(eg: yyyyMMdd)
  static String lastWeekOfDate(String dateStr) {
    DateTime lastDate = DateTime.parse(dateStr).add(const Duration(days: -7));
    String lastDateStr = DateUtil.formatDate(lastDate, format: "yyyyMMdd");

    return lastDateStr;
  }

  /************************************ 报表数据比较日期处理 ************************************/

  /// 今日or昨日 (eg: yyyyMMdd)
  static String lastOrCurrentDayDes(String currDate, bool isLast) {
    DateTime lastDate;
    if (isLast) {
      lastDate = DateTime.parse(currDate).add(const Duration(days: -1));
    } else {
      lastDate = DateTime.parse(currDate);
    }

    String lastDateStr = '${addZero(lastDate.month)}.${addZero(lastDate.day)}';
    return lastDateStr;
  }

  /// 本周or上周 (eg: yyyyMMdd)
  static String lastOrCurrentWeekDes(String currDate, bool isLast) {
    DateTime lastDate1, lastDate2;
    if (isLast) {
      lastDate1 = DateTime.parse(currDate).add(const Duration(days: -7));
      lastDate2 = DateTime.parse(currDate).add(const Duration(days: -1));
    } else {
      lastDate1 = DateTime.parse(currDate);
      lastDate2 = DateTime.parse(currDate).add(const Duration(days: 6));
    }

    String lastDate1Str =
        '${addZero(lastDate1.month)}.${addZero(lastDate1.day)}';
    String lastDate2Str =
        '${addZero(lastDate2.month)}.${addZero(lastDate2.day)}';
    return '$lastDate1Str~$lastDate2Str';
  }

  /// 本月or上月 (eg: yyyyMMdd)
  static String lastOrCurrentMonthDes(String currDate, bool isLast) {
    int month = int.parse(currDate.substring(4, 6));

    if (isLast) {
      month = month - 1;
      if (month <= 0) {
        //int year = int.parse(currDate.substring(0,4));
        month = 12;
        //return '$year.${_twoDigits(month)}';
      }
    }

    return addZero(month);
  }

  /// 格式化 DateTime 为指定格式，支持中文和英文格式切换
  static String dateFormat(DateTime? dateTime, {String format = 'yyyy-MM-dd'}) {
    if (dateTime == null) return "";
    return DateUtil.formatDate(dateTime, format: format);
  }

  /// 格式化 DateTime 为指定格式，支持中文和英文格式切换
  static String dateFormatLocale(DateTime? dateTime,
      {String format = 'yyyy-MM-dd HH:mm:ss',
      String enFormat = 'dd MM,yyyy HH:mm:ss',
      String? locale}) {
    if (dateTime == null) return "";
    locale = locale ?? HPLocalization.getLocale();
    if (locale == 'en-US') {
      // 英文格式特殊处理
      return _formatWithEnglishSuffix(dateTime, enFormat);
    } else {
      return DateUtil.formatDate(dateTime, format: format);
    }
  }

  /// 解析字符串为指定格式
  static String stringFormat(String? dateStr, {String format = 'yyyy-MM-dd'}) {
    if (dateStr == null || dateStr.trim().isEmpty) return "";

    DateTime? time = DateTime.tryParse(dateStr);
    if (time == null) return "";

    return dateFormat(time, format: format);
  }

  /// 解析字符串为指定格式
  static String stringFormatLocale(String? dateStr,
      {String format = 'yyyy-MM-dd HH:mm:ss',
      String enFormat = 'dd MM,yyyy HH:mm:ss',
      String? locale}) {
    if (dateStr == null || dateStr.trim().isEmpty) return "";

    DateTime? time = DateTime.tryParse(dateStr);
    if (time == null) return "";

    locale = locale ?? HPLocalization.getLocale();
    return dateFormatLocale(
      time,
      format: format,
      enFormat: enFormat,
      locale: locale,
    );
  }

  /// 解析字符串为 DateTime
  static DateTime? dateParse(String? dateStr) {
    if (dateStr == null || dateStr.trim().isEmpty) return null;
    return DateTime.tryParse(dateStr);
  }

  /// 英文格式化，包括英文月份缩写和序数后缀
  static String _formatWithEnglishSuffix(DateTime dateTime, String format) {
    String result = format;

    // 替换年月日等字段
    result = result.replaceAll('yyyy', dateTime.year.toString());
    result = result.replaceAll('MM', _monthName(dateTime.month));
    result = result.replaceAll('dd', _dayWithSuffix(dateTime.day));

    // 其他常用替换
    result = result.replaceAll('HH', dateTime.hour.toString().padLeft(2, '0'));
    result =
        result.replaceAll('mm', dateTime.minute.toString().padLeft(2, '0'));
    result =
        result.replaceAll('ss', dateTime.second.toString().padLeft(2, '0'));

    return result;
  }

  /// 月份英文缩写或全名
  static String _monthName(int month) {
    const monthNames = [
      '', // 占位，1~12
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return monthNames[month];
  }

  /// 日期序数后缀
  static String _dayWithSuffix(int day) {
    const dayNames = [
      '', // 占位，1~31
      '1st',
      '2nd',
      '3rd',
      '4th',
      '5th',
      '6th',
      '7th',
      '8th',
      '9th',
      '10th',
      '11th',
      '12th',
      '13th',
      '14th',
      '15th',
      '16th',
      '17th',
      '18th',
      '19th',
      '20th',
      '21st',
      '22nd',
      '23rd',
      '24th',
      '25th',
      '26th',
      '27th',
      '28th',
      '29th',
      '30th',
      '31st',
    ];
    return dayNames[day];
  }

  /// 时间比较大小
  /// print(now.compareTo(future)); // -1
  /// print(now.compareTo(past)); // 1
  /// print(now.compareTo(newDate)); // 0
  static int compareDate(DateTime? date1, DateTime? date2) {
    if (date1 == null || date2 == null) {
      return 0;
    }
    return date1.compareTo(date2);
  }

  /// 增加指定天数
  static DateTime addDays(DateTime? date, int days) {
    if (date == null) {
      return DateTime.now();
    }
    return date.add(Duration(days: days));
  }

  /// 减少指定天数
  static DateTime subtractDays(DateTime? date, int days) {
    if (date == null) {
      return DateTime.now();
    }
    return date.subtract(Duration(days: days));
  }

  /// 增加指定月份（自动处理月末溢出）
  static DateTime addMonths(DateTime? date, int months) {
    if (date == null) {
      return DateTime.now();
    }
    int newYear = date.year + ((date.month + months - 1) ~/ 12);
    int newMonth = (date.month + months - 1) % 12 + 1;
    int newDay = _getSafeDay(newYear, newMonth, date.day);
    return DateTime(newYear, newMonth, newDay);
  }

  /// 减少指定月份
  static DateTime subtractMonths(DateTime? date, int months) {
    if (date == null) {
      return DateTime.now();
    }
    return addMonths(date, -months);
  }

  /// 增加指定年份（自动处理闰年等）
  static DateTime addYears(DateTime? date, int years) {
    if (date == null) {
      return DateTime.now();
    }
    int newYear = date.year + years;
    int newDay = _getSafeDay(newYear, date.month, date.day);
    return DateTime(newYear, date.month, newDay);
  }

  /// 减少指定年份
  static DateTime subtractYears(DateTime? date, int years) {
    if (date == null) {
      return DateTime.now();
    }
    return addYears(date, -years);
  }

  /// 获取目标月的合法最大天数（如 2 月最多是 28/29）
  static int _getSafeDay(int year, int month, int day) {
    int lastDayOfMonth = DateTime(year, month + 1, 0).day;
    return day > lastDayOfMonth ? lastDayOfMonth : day;
  }

  /// 获取某日期当月的第一天
  static DateTime getFirstDayOfMonth(DateTime? date) {
    if (date == null) {
      return DateTime.now();
    }
    return DateTime(date.year, date.month, 1);
  }

  /// 获取某日期当月的最后一天
  static DateTime getLastDayOfMonth(DateTime? date) {
    if (date == null) {
      return DateTime.now();
    }
    // 下个月的第 0 天 = 当前月的最后一天
    return DateTime(date.year, date.month + 1, 0);
  }

  /// 获取某日期当周的第一天
  static DateTime getFirstDayOfWeek([DateTime? date]) {
    final now = date ?? DateTime.now();
    return DateTime(now.year, now.month, now.day)
        .subtract(Duration(days: now.weekday - 1));
  }

  /// 获取某日期当周的最后一天
  static DateTime getLastDayOfWeek([DateTime? date]) {
    final now = date ?? DateTime.now();
    return DateTime(now.year, now.month, now.day)
        .add(Duration(days: 7 - now.weekday));
  }

  /// 获取本年的第一天
  static DateTime getFirstDayOfYear([DateTime? date]) {
    final now = date ?? DateTime.now();
    return DateTime(now.year, 1, 1);
  }

  /// 获取本年的最后一天
  static DateTime getLastDayOfYear([DateTime? date]) {
    final now = date ?? DateTime.now();
    return DateTime(now.year, 12, 31);
  }
}
