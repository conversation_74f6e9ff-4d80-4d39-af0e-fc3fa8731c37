import 'package:auapp/utils/hp_log_util.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/services.dart';

class HPPlugins {
  static final HPPlugins _instance = HPPlugins._();
  static const channelName = 'com.huepay.auapp/flutterPlugin';
  static const channelEventName = '_event_';
  static const channelEventMethod = "name";
  static const channelEventArguments = "arguments";
  late MethodChannel _channel;

  factory HPPlugins() => _shareInstance();

  static _shareInstance() {
    return _instance;
  }

  HPPlugins._() {
    _channel = _channel = const MethodChannel(channelName)
      ..setMethodCallHandler(_nativeMethodHandler);
  }

  Future<dynamic> _nativeMethodHandler(MethodCall call) {
    return Future<dynamic>.value(_filterMethodCall(call));
  }

  ///字符串解密
  Future<String> decryptThreeDESECB(String? resString) async {
    if (TextUtil.isEmpty(resString)) {
      return "";
    }
    Map<dynamic, dynamic> queries = {"desString": resString};
    Map res = await _channel.invokeMethod("hp_decryptThreeDESECB", queries);
    return res['result'] ?? "";
  }

  //原生直接调flutter hp_onBackPress
  dynamic _filterMethodCall(MethodCall call) {
    try {
      HPLogUtil.v(
          "Native 调用 Flutter 成功，_filterChannel 名字${call.method} 参数是：${call.arguments}");
      if (call.method == channelEventName) {
        Map<dynamic, dynamic> msg = call.arguments ?? <dynamic, dynamic>{};
        String methodName = msg[channelEventMethod] ?? "";
        // 同样也是根据方法名分发不同的函数
        switch (methodName) {
          case "hp_onBackPress":
            String msg = call.arguments["msg"];
            HPLogUtil.v("Native 调用 Flutter 成功，名字${call.method} 参数是：$msg");
            return;
          case "hp_onPermissionChange":
            String msg = call.arguments["msg"];
            HPLogUtil.v("权限发生变化时");
            return;
        }
      }
      return null;
    } catch (e) {
      HPLogUtil.v(e);
    }
    return null;
  }
}
