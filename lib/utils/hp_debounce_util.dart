/*
 * ProjectName：UaApp
 * FilePath：lib/utils
 * FileName：hp_debounce_util
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/19 19:43:28
 */

import 'dart:async';

import 'package:auapp/utils/hp_log_util.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';

class HPDebounceUtil {
  static DateTime? _lastPressedAdt; //上次点击时间 初始化的时候是空，下面会做判断
  static Map<String, DateTime> _pressedTagsDateTime =
      new Map<String, DateTime>(); //上次点击时间 初始化的时候是空，下面会做判断

  static final Map<String, int> _lastClickTimeMap = {}; // 保存最后一次点击时间
  static final Map<String, Timer?> _debounceTimers = {}; // 防抖动定时器

  static bool isDoubleClicked(
      {Duration duration = const Duration(milliseconds: 500), String? tag}) {
    DateTime? lastPressedAdt = _lastPressedAdt;
    if (!TextUtil.isEmpty(tag)) {
      lastPressedAdt = _pressedTagsDateTime[tag!];
    }

    if (lastPressedAdt == null ||
        DateTime.now().difference(lastPressedAdt) > duration) {
      HPLogUtil.v("this is not double checked!");
      if (!TextUtil.isEmpty(tag)) {
        _pressedTagsDateTime[tag!] = DateTime.now();
      } else {
        _lastPressedAdt = DateTime.now();
      }
      return false;
    }

    if (!TextUtil.isEmpty(tag)) {
      _pressedTagsDateTime[tag!] = DateTime.now();
    } else {
      _lastPressedAdt = DateTime.now();
    }
    HPLogUtil.v("this is double checked!");
    return true;
  }

  /// 防连点点击（默认300ms）
  /// [key] 唯一标识，不同按钮需要不同key
  /// [interval] 时间间隔（毫秒）
  /// [onPressed] 点击回调
  static VoidCallback throttle(
    String key, {
    int interval = 300,
    required VoidCallback onPressed,
  }) {
    return () {
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      final lastTime = _lastClickTimeMap[key] ?? 0;
      if (currentTime - lastTime > interval) {
        _lastClickTimeMap[key] = currentTime;
        onPressed();
      }
    };
  }

  /// 异步防连点（适用于需要等待异步操作完成的场景）
  /// [key] 唯一标识
  /// [interval] 时间间隔
  /// [onPressed] 异步点击回调
  static VoidCallback asyncThrottle(
    String key, {
    int interval = 300,
    required Future<void> Function() onPressed,
  }) {
    bool isAvailable = true;

    return () async {
      if (!isAvailable) return;
      isAvailable = false;
      try {
        await onPressed();
      } finally {
        Future.delayed(Duration(milliseconds: interval), () {
          isAvailable = true;
        });
      }
    };
  }

  /// 防抖动点击（默认300ms）
  /// [key] 唯一标识
  /// [delay] 延迟时间（毫秒）
  /// [onPressed] 点击回调
  static VoidCallback debounce(
    String key, {
    int delay = 300,
    required VoidCallback onPressed,
  }) {
    return () {
      // 取消前一个定时器
      if (_debounceTimers[key]?.isActive ?? false) {
        _debounceTimers[key]?.cancel();
      }

      // 设置新的定时器
      _debounceTimers[key] = Timer(Duration(milliseconds: delay), () {
        onPressed();
      });
    };
  }

  /// 清除防抖定时器（页面销毁时调用）
  static void dispose(String key) {
    _debounceTimers[key]?.cancel();
    _debounceTimers.remove(key);
    _lastClickTimeMap.remove(key);
  }
}
