/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-22 11:10:35
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-22 11:11:15
 * @FilePath: /hwicc-mobile-flutter/lib/utils/hp_amount_util.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:intl/intl.dart';

class HPAmountUtil {
  /// 千分位格式化（带小数点）
  static String formatWithComma(num? amount,
      {int decimal = 2, String defaultVal = '0.00'}) {
    if (amount == null) return '0.00';
    try {
      NumberFormat formatter = NumberFormat.currency(
        locale: 'en_US',
        symbol: '',
        decimalDigits: decimal,
      );
      return formatter.format(amount);
    } catch (e) {
      return defaultVal; // 解析失败则返回空字符串
    }
  }

  /// 千分位格式化（带小数点）
  static String formatWithCommaDeftVal(String? amountStr,
      {int decimal = 2, String defaultVal = '0.00'}) {
    if (amountStr == null || amountStr.isEmpty) return defaultVal;
    try {
      num amount = double.parse(amountStr);
      NumberFormat formatter = NumberFormat.currency(
        locale: 'en_US',
        symbol: '',
        decimalDigits: decimal,
      );
      return formatter.format(amount);
    } catch (e) {
      return defaultVal; // 解析失败则返回空字符串
    }
  }

  /// 金额取绝对值
  static num abs(num? amount) {
    if (amount == null) return 0;
    return amount.abs();
  }

  /// 保留两位小数（不足补0）
  static String formatDecimal(num? amount, {int decimal = 2}) {
    if (amount == null) return '0.00';
    return amount.toStringAsFixed(decimal);
  }

  /// 综合处理：先绝对值 → 再千分位格式化 → 再保留两位小数
  static String formatAbsComma(num? amount, {bool absValue = false}) {
    if (amount == null) return '0.00';
    num val = absValue ? amount.abs() : amount;
    return formatWithComma(val, decimal: 2);
  }
}
