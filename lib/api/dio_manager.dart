/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-21 09:59:01
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-21 09:59:35
 * @FilePath: /hwicc-mobile-flutter/lib/api/dio_manager.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:dio/dio.dart';

class DioManager {
  static final Map<String, Dio> _dioMap = {};

  static Dio getDio({
    required String baseUrl,
    List<Interceptor>? interceptors,
    Map<String, dynamic>? defaultHeaders,
  }) {
    if (_dioMap.containsKey(baseUrl)) {
      return _dioMap[baseUrl]!;
    }

    final options = BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: 60000,
      receiveTimeout: 60000,
      headers: defaultHeaders ?? {'Content-Type': 'application/json'},
    );

    final dio = Dio(options);

    // 加载全局或自定义拦截器
    if (interceptors != null) {
      dio.interceptors.addAll(interceptors);
    }

    _dioMap[baseUrl] = dio;
    return dio;
  }
}
