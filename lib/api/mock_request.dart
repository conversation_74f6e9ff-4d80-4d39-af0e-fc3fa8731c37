/*
 * ProjectName：UaApp
 * FilePath：lib/api
 * FileName：mock_request
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/18 09:55:26
 */

import 'dart:convert';

import 'package:flutter/services.dart';

class MockRequest {
  static Future<Map<String, dynamic>> mockRequest<T>(String mockFileName) async {
    // 1. 读取 JSON 字符串
    final jsonString = await rootBundle.loadString('assets/mock/${mockFileName}');
    // 2. 解析为 Map
    final jsonMap = jsonDecode(jsonString) as Map<String, dynamic>;
    return jsonMap;
  }
}