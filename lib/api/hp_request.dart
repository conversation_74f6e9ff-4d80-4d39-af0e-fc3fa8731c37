/*
 * ProjectName：UaApp
 * FilePath：lib/api
 * FileName：hp_request
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/26 17:46:52
 */

import 'dart:convert';

import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/dio_manager.dart';
import 'package:auapp/api/model/error_response.dart';
import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/models/HPUserInfoSingleton.dart';
import 'package:auapp/pub/constants/storage_constants.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/services/app_info_service.dart';
import 'package:auapp/services/storage_service.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:common_utils/common_utils.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response, FormData, MultipartFile;

class HPRequest {
  static const String defaultBaseUrl = ApiConstants.baseUrl;

  static Future<HPResponse> _request<T>(
      String url, {
        required String method,
        dynamic params,
        bool showLoading = true,
        String? customBaseUrl,
        Map<String, dynamic>? headers,
        List<Interceptor>? interceptors,
      }) async {
    if (showLoading) HPLoading.show();

    final dio = DioManager.getDio(
      baseUrl: customBaseUrl ?? defaultBaseUrl,
      interceptors: interceptors ?? [_globalInterceptor],
    );

    final options = Options(method: method, headers: headers);
    print("请求入参：${customBaseUrl ?? defaultBaseUrl}${url} → ${params}");
    try {
      final response = await dio.request(
        url,
        data: method.toUpperCase() == 'GET' ? null : params,
        queryParameters: method.toUpperCase() == 'GET' ? params : null,
        options: options,
      );

      final data = HPResponse.fromJSON(response.data);
      return data;
    } on DioError catch (e, st) {
      ErrorResponse error = ErrorResponse.fromJson(e.response?.data ?? {});
      HPResponse response = HPResponse(
        code: error.status.toString() ?? "-1",
        msg: "network_error_tips".tr,
        success: false,
        data: {},
      );
      return response;
    } finally {
      if (showLoading) HPLoading.dismiss();
    }
  }

  /// 全局拦截器
  static Interceptor get _globalInterceptor => InterceptorsWrapper(
    onRequest: (options, handler) {
      String language =
          StorageService.getString(StorageConstants.huepayLanguage) ??
              "en-US";
      String token = StorageService.getString(StorageConstants.token) ?? "";
      Map appInfo = Get.find<AppInfoService>().getAppInfoJson();

      options.headers.addAll({
        "accept-language": language,
        "platform_id": "MERAP",
        "platform_type": appInfo['app_platform'] ?? '',
        "app_version": appInfo['app_version'] ?? '',
        "device_id": appInfo['device_id'] ?? '',
        "hf_token": token,
      });

      LogUtil.d(
          "请求头：${options.baseUrl}${options.path} → ${options.headers}");
      return handler.next(options);
    },
    onResponse: (res, handler) {
      LogUtil.d(
          "响应：${res.requestOptions.baseUrl}${res.requestOptions.path} → ${json.encode(res.data)}");
      // 当响应码 01030998 代表登录过期
      if (res.data['code'] == '01030998') {
        StorageService.setBool(StorageConstants.loginFlag, false);
        StorageService.remove(StorageConstants.token);
        StorageService.remove(StorageConstants.userInfo);
        StorageService.remove(StorageConstants.loginOperatorInfo);
        StorageService.remove(StorageConstants.loginOperatorExtInfo);
        HPUserInfoSingleton().clearUserInfo();
        HPRouter.resolvePage(Routes.login);
      }
      return handler.next(res);
    },
    onError: (e, handler) {
      LogUtil.e(
          "请求错误：${e.requestOptions.baseUrl}${e.requestOptions.path} → ${e.message}");
      return handler.next(e);
    },
  );

  /// GET
  static Future<HPResponse> get<T>(
      String url, {
        Map<String, dynamic>? params,
        bool showLoading = true,
        String? customBaseUrl,
        Map<String, dynamic>? headers,
        List<Interceptor>? interceptors,
      }) =>
      _request(
        url,
        method: 'GET',
        params: params,
        showLoading: showLoading,
        customBaseUrl: customBaseUrl,
        headers: headers,
        interceptors: interceptors,
      );

  /// POST
  static Future<HPResponse> post<T>(
      String url, {
        dynamic params,
        bool showLoading = true,
        String? customBaseUrl,
        Map<String, dynamic>? headers,
        List<Interceptor>? interceptors,
      }) =>
      _request(
        url,
        method: 'POST',
        params: params,
        showLoading: showLoading,
        customBaseUrl: customBaseUrl,
        headers: headers,
        interceptors: interceptors,
      );

  /// 上传文件
  static Future<HPResponse> upload<T>(
      String url, {
        required Map<String, dynamic> fileMap,
        Map<String, dynamic>? fields,
        bool showLoading = true,
        String? customBaseUrl,
        List<Interceptor>? interceptors,
      }) {
    final formData = FormData.fromMap({
      ...?fields,
      ...fileMap.map((k, v) => MapEntry(k, MultipartFile.fromFileSync(v))),
    });

    return _request(
      url,
      method: 'POST',
      params: formData,
      showLoading: showLoading,
      customBaseUrl: customBaseUrl,
      interceptors: interceptors,
    );
  }
}
