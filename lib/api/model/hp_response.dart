//
import 'dart:convert';

class HPResponse<T> {
  final String code;
  final String msg;
  final bool success;
  final num? total; // 列表接口可能会返回
  final T? data;

  String get stateCode => code;
  bool get isSuccess => code == '00000000'; // 自行定义成功 code

  HPResponse({
    this.code = "",
    this.msg = "",
    this.success = false,
    this.total,
    this.data,
  });

  HPResponse.error({required String msg})
      : this(
          code: '',
          msg: msg,
          success: false,
          data: null,
        );

  factory HPResponse.fromJSON(Map<String, dynamic> data) {
    return HPResponse(
      code: data['code'] ?? "",
      msg: data['msg'] ?? "",
      success: data['success'] ?? false,
      total: data['total'] ?? 0,
      data: data['data'],
    );
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = {};
    try {
      map["code"] = code;
      map["msg"] = msg;
      map["success"] = success;
      map["total"] = total;
      map["data"] = data;
    } catch (e) {
      print(e);
    }
    return map;
  }

  @override
  String toString() {
    return json.encode(toJson());
  }
}
