class ErrorResponse {
  final num? status;
  final String error;
  final String message;

  ErrorResponse({
    this.status,
    this.error = "Network Error",
    this.message = "Network Error",
  });

  factory ErrorResponse.fromJson(Map<String, dynamic> json) {
    return ErrorResponse(
      status: json['status'],
      error: json['error'] ?? "Network Error",
      message: json['message'] ?? "Network Error",
    );
  }
  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = {};
    try {
      map["status"] = status;
      map["error"] = error;
      map["message"] = message;
    } catch (e) {
      print(e);
    }
    return map;
  }
}
