/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-20 17:30:31
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-21 19:22:05
 * @FilePath: /hwicc-mobile-flutter/lib/api/api_constants.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
class ApiConstants {
  /// 网络请求BaseUrl
  // static const baseUrl = 'https://acquire.uprimer.com/api';
  /// st1
  // static const baseUrl = 'http://************:31855';
  /// st2
  // static const baseUrl = 'http://************:31856';
  /// st3
  static const baseUrl = 'http://************:31857';

  // 交易模块baseUrl
  static const baseTransactionUrl = 'http://************:31861';

  /// 登录模块
  static const loginUrl = '/login';

  /// 获取极验图形验证码
  static const loginCaptchaUrl = '/login/capt';

  /// （登录/忘记密码）获取验证码
  static const sendVerifyCodeUrl = '/login/captcha';

  /// 退出登录
  static const logoutUrl = '/logout';

  /// 注销账号
  static const closeAccountUrl = '/user/cancel';

  /// 获取登录操作员信息接口
  static const appLoginUserInfoUrl = "/user/info";

  /// 获取操作员详细信息查询接口
  static const appExtUserDetailInfoUrl = "/user/extuserdetail";

  /// 获取子用户列表
  static const getUserSwitchListUrl = '/user/switch/list';

  /// 切换子用户
  static const switchUserUrl = '/user/switch';

  /// 统计存在交易的日期列表
  static const transactionDayDataUrl =
      '/api/transaction/statisticDayDataStatus';

  /// 查询交易列表
  static const transactionInfoListUrl = "/api/transaction/list";

  /// 汇总统计
  static const transactionInfoSummaryUrl = "/api/summary/merSummary";

  /// 结算汇总
  static const settleSummaryUrl = "/api/settle/sum";

  /// 结算批次列表
  static const settleBatchListUrl = "/api/settle/batch/list";

  /// 交易详情接口
  static const transactionDetailInfoUrl = "/api/transaction/detail";

  /// 退款
  static const transactionRefundUrl = "/api/transaction/refund";

  /// 退款结果
  static const transactionRefundResultInfoUrl = "/api/transaction/queryRefundResult";

}
