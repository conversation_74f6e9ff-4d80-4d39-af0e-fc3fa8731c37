/*
 * ProjectName：UaApp
 * FilePath：lib/vendor
 * FileName：gt3_captcha_util
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/19 18:25:20
 */

import 'dart:io';

import 'package:gt3_flutter_plugin/gt3_flutter_plugin.dart';

import '../pub/constants/storage_constants.dart';
import '../services/storage_service.dart';

class Gt3CaptchaUtil {
  static const String TAG = "GtsUtil";

  static late Gt3FlutterPlugin captcha;

  static void init() {
    String language =
        StorageService.getString(StorageConstants.huepayLanguage) ?? "en-US";
    /// 初始化极验配置
    Gt3CaptchaConfig config = Gt3CaptchaConfig();
    config.language = language == "en-US" ? 'en' : language; // 设置语言为英文 Set English as the CAPTCHA language
    config.cornerRadius = 5.0; // 设置圆角大小为 5.0 Set the corner radius to 5.0
    config.timeout = 5.0; // 设置每个请求的超时时间为 5.0 Set the timeout for each request to 5.0 seconds
    captcha = Gt3FlutterPlugin(config);
  }

  static void startCaptcha(String gt, String challenge) {
    // 从服务端接口获取验证参数 Get validation parameters from the server API
    Gt3RegisterData registerData = Gt3RegisterData(
        gt: gt, // 验证ID，从极验后台创建 Verify ID, created from the geetest dashboard
        challenge: challenge, // 从极验服务动态获取 Gain challenges from geetest
        success: true); // 对极验服务的心跳检测 Check if it is success
    print('调用极验：参数 gt：${gt} -- challenge ${challenge}');
    captcha.startCaptcha(registerData);
  }

  /// 关闭验证
  static void closeCaptcha() {

  }

}