/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-14 18:20:09
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-16 11:12:29
 * @FilePath: /hwicc-mobile-flutter/lib/lang/zh_CN.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:auapp/lang/modules/comment.dart';
import 'package:auapp/lang/modules/home.dart';
import 'package:auapp/lang/modules/lang_setting.dart';
import 'package:auapp/lang/modules/lang_transaction_detail.dart';
import 'package:auapp/lang/modules/login.dart';
import 'package:auapp/lang/modules/transaction.dart';
import 'package:auapp/lang/modules/choose.dart';
import 'package:auapp/lang/modules/mine.dart';
import 'package:auapp/lang/modules/settlement.dart';

/// 简体中文
const Map<String, String> zhCN = {
  ...zhCNLogin,
  ...zhCNComment,
  ...zhCNHome,
  ...zhCNTransaction,
  ...zhCNTransactionDetail,
  ...zhCNChoose,
  ...zhCNMine,
  ...zhCNSettlement,
  ...zhCNSetting,
};
