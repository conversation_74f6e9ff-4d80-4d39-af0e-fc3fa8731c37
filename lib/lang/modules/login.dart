/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-16 11:02:36
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-16 11:11:08
 * @FilePath: /hwicc-mobile-flutter/lib/lang/en_US/login.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Map<String, String> enUSLogin = {
  'splash_skip': 'Skip',
  'login_account_type_mobile': 'Login with mobile number',
  'login_account_type_mobile_bottom': 'Mobile number login',
  'login_account_type_email': 'Login with email',
  'login_account_type_email_bottom': 'Email login',
  'login_button_title': 'Sign in',
  'login_mobile_tips': 'Mobile number',
  'login_input_mobile_placeholder': 'Enter mobile number',
  'login_password_tips': 'Password',
  'login_input_password_placeholder': 'Enter login password',
  'login_email_address_tips': 'Email address',
  'login_input_email_placeholder': 'Enter email address',
  'login_verify_code': 'Code',
  'login_verify_code_placeholder': 'Enter code',
  'login_pwd_type_password': 'Password Login',
  'login_pwd_type_code': 'Code Login',
  'login_send_code_title': 'Send code',
  'login_resend_code_title': 'Resend code',
  'login_send_code_tips': 'Send code first',
  'login_send_code_success_tips': 'Send code successfully',
  'login_forget_pwd_title': 'Forget password',
  'login_change_login_type_title': 'Other login methods',
  'login_protocol_tips': 'I Agree',
  'login_protocol_privacy': ' Privacy Policy ',
  'login_email_format_error': 'The email is invalid',
  'login_email_error': 'Invalid email',
  'login_mobile_format_error': 'The mobile is invalid',
  'login_select_language': 'Language',
  'login_select_region': 'Country / Region Code',
  'login_region_country_china': 'China',
  'login_region_country_australia': 'Australia',
  'login_region_country_hongkong': 'China,Hong Kong',
  'login_agree_privacy_tips': 'Please check the 《Privacy Policy》 first',
};
const Map<String, String> zhCNLogin = {
  'splash_skip': '跳过',
  'login_account_type_mobile': '手机号登录',
  'login_account_type_mobile_bottom': '手机号登录',
  'login_account_type_email': '邮箱登录',
  'login_account_type_email_bottom': '邮箱登录',
  'login_button_title': '登录',
  'login_mobile_tips': '手机号',
  'login_input_mobile_placeholder': '请输入手机号',
  'login_password_tips': '密码',
  'login_input_password_placeholder': '请输入账号密码',
  'login_email_address_tips': '邮箱',
  'login_input_email_placeholder': '请输入邮箱账号',
  'login_verify_code': '验证码',
  'login_verify_code_placeholder': '请输入验证码',
  'login_pwd_type_password': '密码登录',
  'login_pwd_type_code': '验证码登录',
  'login_send_code_title': '发送验证码',
  'login_resend_code_title': '重新发送',
  'login_send_code_tips': '请先发送验证码',
  'login_send_code_success_tips': '验证码发送成功，请注意查收',
  'login_forget_pwd_title': '忘记密码',
  'login_change_login_type_title': '其它登录方式',
  'login_protocol_tips': '已阅读并同意',
  'login_protocol_privacy': ' 隐私政策 ',
  'login_email_format_error': '邮箱格式不正确，请修改',
  'login_email_error': '邮箱错误',
  'login_mobile_format_error': '手机号格式不正确，请修改',
  'login_select_language': '选择语言',
  'login_select_region': '选择国家和地区码',
  'login_region_country_china': '中国',
  'login_region_country_australia': '澳大利亚',
  'login_region_country_hongkong': '中国香港',
  'login_agree_privacy_tips': '请先阅读并同意隐私协议',
};
const Map<String, String> zhHKLogin = {
  'splash_skip': '跳過',
  'login_account_type_mobile': '手機號登錄',
  'login_account_type_mobile_bottom': '手機號登錄',
  'login_account_type_email': '郵箱登錄',
  'login_account_type_email_bottom': '郵箱登錄',
  'login_page_title': '登錄',
  'login_button_title': '登錄',
  'login_mobile_tips': '手機號',
  'login_input_mobile_placeholder': '請輸入手機號',
  'login_password_tips': '密碼',
  'login_input_password_placeholder': '請輸入賬號密碼',
  'login_email_address_tips': '郵箱',
  'login_input_email_placeholder': '請輸入郵箱賬號',
  'login_verify_code': '驗證碼',
  'login_verify_code_placeholder': '請輸入驗證碼',
  'login_pwd_type_password': '密碼登錄',
  'login_pwd_type_code': '驗證碼登錄',
  'login_send_code_title': '發送驗證碼',
  'login_resend_code_title': '重新發送',
  'login_send_code_tips': '請先發送驗證碼',
  'login_send_code_success_tips': '驗證碼發送成功，請注意查收',
  'login_forget_pwd_title': '忘記密碼',
  'login_change_login_type_title': '其它登錄方式',
  'login_protocol_tips': '已閱讀並同意',
  'login_protocol_privacy': ' 隱私政策 ',
  'login_email_format_error': '郵箱格式不正確，請修改',
  'login_email_error': '郵箱錯誤',
  'login_mobile_format_error': '手機號格式不正確，請修改',
  'login_select_language': '選擇語言',
  'login_select_region': '選擇國家和地區碼',
  'login_region_country_china': '中國',
  'login_region_country_australia': '澳大利亞',
  'login_region_country_hongkong': '中國香港',
  'login_agree_privacy_tips': '請先閱讀並同意隱私協議',
};
