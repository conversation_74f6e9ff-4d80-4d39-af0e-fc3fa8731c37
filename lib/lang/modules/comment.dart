/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-16 11:02:36
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-20 16:07:13
 * @FilePath: /hwicc-mobile-flutter/lib/lang/en_US/login.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Map<String, String> enUSComment = {
  'main_tab_title_home': 'Home',
  'main_tab_title_transaction': 'Transaction',
  'main_tab_title_reports': 'Reports',
  'main_tab_title_mine': 'Me',
  'refresh_load_more': 'Pull up to load more',
  'refresh_loading': 'Loading...',
  'refresh_no_data': 'This is the bottom',
  'refresh_load_failed': 'Load failed',
  'network_error_tips': 'Network error',
  'common_btn_confirm': 'Confirm',
  'common_btn_cancel': 'Cancel',
  'common_btn_save': 'Save',
};
const Map<String, String> zhCNComment = {
  'main_tab_title_home': '首页',
  'main_tab_title_transaction': '账本',
  'main_tab_title_reports': '报表',
  'main_tab_title_mine': '我的',
  'refresh_load_more': '上拉加载更多',
  'refresh_loading': '加载中...',
  'refresh_no_data': '已经到底了',
  'refresh_load_failed': '加载失败',
  'network_error_tips': '网络连接异常，请检查网络',
  'common_btn_confirm': '确认',
  'common_btn_cancel': '取消',
  'common_btn_save': '保存',
};
const Map<String, String> zhHKComment = {
  'main_tab_title_home': '首頁',
  'main_tab_title_transaction': '賬本',
  'main_tab_title_reports': '報表',
  'main_tab_title_mine': '我的',
  'refresh_load_more': '上拉加載更多',
  'refresh_loading': '加載中...',
  'refresh_no_data': '已經到底了',
  'refresh_load_failed': '加載失敗',
  'network_error_tips': '網絡連接異常，請檢查網絡',
  'common_btn_confirm': '確認',
  'common_btn_cancel': '取消',
  'common_btn_save': '保存',
};
