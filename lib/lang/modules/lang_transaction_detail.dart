/*
 * ProjectName：UaApp
 * FilePath：lib/lang/modules
 * FileName：lang_transaction_detail
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/23 13:18:35
 */

const Map<String, String> enUSTransactionDetail = {
  "transaction_detail_title": "Detail",
  "transaction_detail_refund": "Refund",
  "transaction_detail_order_sale_amt_lb": "Order amount",
  "transaction_detail_order_refund_amt_lb": "Refund amount",
  "transaction_type_lb": "Transaction type",
  "transaction_type_sale": "Sale",
  "transaction_type_refund": "Refund",
  "transaction_order_status_lb": "Order status",
  "transaction_order_status_success": "Succeed",
  "transaction_order_status_failed": "Failed",
  "transaction_order_status_Pending": "Pending",
  "transaction_service_fee_sale_lb": "Merchant service fee",
  "transaction_service_fee_refund_lb": "Refund Merchant fee",
  "transaction_customer_bear_fee_lb": "Customer bears the service fee",
  "transaction_settled_amt_lb": "Settlement amount",
  "transaction_detail_merchant_name_lb": "Merchant name",
  "transaction_detail_payment_method_lb": "Payment method",
  "transaction_date_local_time_lb": "Transaction time(loacl)",
  "transaction_date_cn_time_lb": "Transaction time(CN)",
  "transaction_customer_id_lb": "Customer ID",
  "transaction_huepay_order_id_lb": "HuePay order ID",
  "transaction_external_id_lb": "External transaction ID",
  "transaction_detail_remark_lb": "Remark",
  "transaction_detail_refunds_list_lb": "Refund list",
  "transaction_detail_origin_order_lb": "Transaction info",
  "transaction_check_order_detail_btn": "Detail",
  "transaction_content_copy_btn": "Copy",
  "transaction_copy_success_tips": "Copied to the clipboard",
  "transaction_refund_confirm_btn": "Confirm",
  "transaction_refund_amount_lb": "Refund amount",
  "transaction_refund_remark_lb": "Refund remark",
  "transaction_refund_add_remark_lb": "Add",
  "transaction_refund_edit_remark_lb": "Edit",
  "transaction_refund_save_remark_lb": "Save",
  "transaction_refund_all_btn": "Refund All",
  "transaction_refund_input_placeholder": "Please input refund amount",
  "transaction_refund_remark_placeholder": "Please input refund remark",
  "transaction_refundable_amount_lb": "Refundable amount",
  "transaction_refund_password_popup_title": "Refund password",
  "transaction_refund_password_popup_input_placeholder": "Please input refund password",
  "transaction_refund_twice_confirm_dialog_title": "Please confirm again",
  "transaction_refund_twice_confirm_dialog_content_start": "You are initiating a refund. The refund amount is ",
  "transaction_refund_twice_confirm_dialog_content_end": ". Please confirm whether you want to proceed with the refund.",

};
const Map<String, String> zhCNTransactionDetail = {
  "transaction_detail_title": "交易详情",
  "transaction_detail_refund": "退款",
  "transaction_detail_order_sale_amt_lb": "订单金额",
  "transaction_detail_order_refund_amt_lb": "退款金额",
  "transaction_type_lb": "交易类型",
  "transaction_type_sale": "消费",
  "transaction_type_refund": "退款",
  "transaction_order_status_lb": "订单状态",
  "transaction_order_status_success": "成功",
  "transaction_order_status_failed": "失败",
  "transaction_order_status_Pending": "处理中",
  "transaction_service_fee_sale_lb": "商户服务费",
  "transaction_service_fee_refund_lb": "返还商户手续费",
  "transaction_customer_bear_fee_lb": "客户承担服务费",
  "transaction_settled_amt_lb": "结算金额",
  "transaction_detail_merchant_name_lb": "商户名称",
  "transaction_detail_payment_method_lb": "付款方式",
  "transaction_date_local_time_lb": "交易时间(当地)",
  "transaction_date_cn_time_lb": "交易时间(中国)",
  "transaction_customer_id_lb": "客户ID",
  "transaction_huepay_order_id_lb": "HuePay订单ID",
  "transaction_external_id_lb": "对外交易ID",
  "transaction_detail_remark_lb": "备注",
  "transaction_detail_refunds_list_lb": "退款列表",
  "transaction_detail_origin_order_lb": "原交易",
  "transaction_check_order_detail_btn": "详情",
  "transaction_content_copy_btn": "复制",
  "transaction_copy_success_tips": "已复制粘贴板",
  "transaction_refund_confirm_btn": "确认",
  "transaction_refund_amount_lb": "退款金额",
  "transaction_refund_remark_lb": "退款备注",
  "transaction_refund_add_remark_lb": "添加备注",
  "transaction_refund_edit_remark_lb": "编辑",
  "transaction_refund_save_remark_lb": "保存",
  "transaction_refund_all_btn": "全部",
  "transaction_refund_input_placeholder": "请输入退款金额",
  "transaction_refund_remark_placeholder": "请输入退款备注",
  "transaction_refundable_amount_lb": "可退金额",
  "transaction_refund_password_popup_title": "退款密码",
  "transaction_refund_password_popup_input_placeholder": "请输入退款密码",
  "transaction_refund_twice_confirm_dialog_title": "请再次确认",
  "transaction_refund_twice_confirm_dialog_content_start": "你正在发起一笔退款。退款金额为 ",
  "transaction_refund_twice_confirm_dialog_content_end": "。请确认你是否要继续进行退款操作。",

};
const Map<String, String> zhHKTransactionDetail = {
  "transaction_detail_title": "交易詳情",
  "transaction_detail_refund": "退款",
  "transaction_detail_order_sale_amt_lb": "訂單金額",
  "transaction_detail_order_refund_amt_lb": "退款金額",
  "transaction_type_lb": "交易類型",
  "transaction_type_sale": "消費",
  "transaction_type_refund": "退款",
  "transaction_order_status_lb": "訂單狀態",
  "transaction_order_status_success": "成功",
  "transaction_order_status_failed": "失敗",
  "transaction_order_status_Pending": "處理中",
  "transaction_service_fee_sale_lb": "商戶服務費",
  "transaction_service_fee_refund_lb": "返還商戶手續費",
  "transaction_customer_bear_fee_lb": "客戶承擔服務費",
  "transaction_settled_amt_lb": "結算金額",
  "transaction_detail_merchant_name_lb": "商戶名稱",
  "transaction_detail_payment_method_lb": "付款方式",
  "transaction_date_local_time_lb": "交易時間(當地)",
  "transaction_date_cn_time_lb": "交易時間(中國)",
  "transaction_customer_id_lb": "客戶ID",
  "transaction_huepay_order_id_lb": "HuePay訂單ID",
  "transaction_external_id_lb": "對外交易ID",
  "transaction_detail_remark_lb": "備注",
  "transaction_detail_refunds_list_lb": "退款列表",
  "transaction_detail_origin_order_lb": "原交易",
  "transaction_check_order_detail_btn": "詳情",
  "transaction_content_copy_btn": "復制",
  "transaction_copy_success_tips": "已復制粘貼板",
  "transaction_refund_confirm_btn": "確認",
  "transaction_refund_amount_lb": "退款金額",
  "transaction_refund_remark_lb": "退款備注",
  "transaction_refund_add_remark_lb": "添加備注",
  "transaction_refund_edit_remark_lb": "編輯",
  "transaction_refund_save_remark_lb": "保存",
  "transaction_refund_all_btn": "全部",
  "transaction_refund_input_placeholder": "請輸入退款金額",
  "transaction_refund_remark_placeholder": "請輸入退款備注",
  "transaction_refundable_amount_lb": "可退金額",
  "transaction_refund_password_popup_title": "退款密碼",
  "transaction_refund_password_popup_input_placeholder": "請輸入退款密碼",
  "transaction_refund_twice_confirm_dialog_title": "請再次確認",
  "transaction_refund_twice_confirm_dialog_content_start": "你正在發起一筆退款。退款金額爲 ",
  "transaction_refund_twice_confirm_dialog_content_end": "。請確認你是否要繼續進行退款操作。",

};
