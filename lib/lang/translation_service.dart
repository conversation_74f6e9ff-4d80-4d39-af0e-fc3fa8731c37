
import 'dart:ui';

import 'package:get/get.dart';
import 'package:auapp/lang/zh_CN.dart';
import 'package:auapp/lang/zh_HK.dart';

import 'en_US.dart';

class TranslationService extends Translations {
  static Locale? get locale => Get.deviceLocale;
  static const fallbackLocale = Locale('en', 'US');
  @override
  Map<String, Map<String, String>> get keys => {
    'en_US': enUS,
    'zh_CN': zhCN,
    'zh_HK': zhHK,
  };
}
