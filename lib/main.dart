/*
 * ProjectName：UaApp
 * FilePath：lib
 * FileName：main
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/14 15:30:44
 */
import 'package:auapp/widgets/smart_refresher_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:auapp/config/application_initialize.dart';
import 'package:auapp/routes/app_pages.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'base/base_binding.dart';
import 'lang/translation_service.dart';
import 'package:flutter/services.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  /// 初始化配置
  await ApplicationInitialize.init();
// 设置首选方向
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]).then((_) {
    runApp(const MyApp());
  });

  /// 配置Loading
  configLoading();
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context,
        designSize: Size(UiSize.isIpad ? 750.0 : 375.0, 667));

    return RefreshConfiguration(
      headerBuilder: () =>
          const HPRefreshHeader(), // 配置默认头部指示器,假如你每个页面的头部指示器都一样的话,你需要设置这个
      footerBuilder: () => const HPRefreshFooter(), // 配置默认底部指示器
      headerTriggerDistance: 80.0, // 头部触发刷新的越界距离
      springDescription: const SpringDescription(
          stiffness: 170,
          damping: 16,
          mass: 1.9), // 自定义回弹动画,三个属性值意义请查询flutter api
      maxOverScrollExtent: 100, //头部最大可以拖动的范围,如果发生冲出视图范围区域,请设置这个属性
      maxUnderScrollExtent: 100, // 底部最大可以拖动的范围
      enableScrollWhenRefreshCompleted:
          true, //这个属性不兼容PageView和TabBarView,如果你特别需要TabBarView左右滑动,你需要把它设置为true
      enableLoadingWhenFailed: true, //在加载失败的状态下,用户仍然可以通过手势上拉来触发加载更多
      hideFooterWhenNotFull: false, // Viewport不满一屏时,禁用上拉加载更多功能
      enableBallisticLoad: true, // 可以通过惯性滑动触发加载更多
      child: GetMaterialApp(
        debugShowCheckedModeBanner: false,
        enableLog: true,
        initialRoute: Routes.splash,
        defaultTransition: Transition.rightToLeft,
        getPages: AppPages.routes,
        initialBinding: BaseBinding(),
        smartManagement: SmartManagement.keepFactory,
        title: 'Huepay',
        theme: ThemeConfig.lightTheme,
        locale: TranslationService.locale,
        fallbackLocale: TranslationService.fallbackLocale,
        translations: TranslationService(),
        builder: EasyLoading.init(),
      ),
    );
  }
}

void configLoading() {
  EasyLoading.instance
    ..displayDuration = const Duration(milliseconds: 1500)
    ..indicatorType = EasyLoadingIndicatorType.ring
    ..loadingStyle = EasyLoadingStyle.custom
    ..indicatorSize = 45.0
    ..radius = 10.0
    ..progressColor = Colors.white
    ..backgroundColor = const Color(0x66000000)
    ..indicatorColor = Colors.white
    ..textColor = Colors.white
    ..lineWidth = 4
    ..userInteractions = false;
}
