import 'package:auapp/r.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HPAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? text;
  final Widget? title;
  final List<Widget>? actions;
  final Color? backgroundColor;
  final double elevation;
  final Color? shadowColor;
  final IconThemeData? iconTheme;
  final bool centerTitle;
  final Widget? leading;
  final Color? backColor;
  final bool showLeading;
  final SystemUiOverlayStyle? systemOverlayStyle;
  final VoidCallback? backFunc;

  const HPAppBar({
    Key? key,
    this.text,
    this.title,
    this.actions,
    this.backgroundColor,
    this.elevation = 0,
    this.shadowColor,
    this.iconTheme,
    this.centerTitle = true,
    this.leading,
    this.backColor,
    this.showLeading = true,
    this.systemOverlayStyle,
    this.backFunc,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      systemOverlayStyle: systemOverlayStyle ?? SystemUiOverlayStyle.dark,
      automaticallyImplyLeading: false,
      title: title ??
          Text(
            text ?? '',
            style: ThemeConfig.boldBackText18,
          ),
      actions: actions,
      backgroundColor: backgroundColor ?? Colors.transparent,
      elevation: elevation,
      shadowColor: shadowColor ?? Colors.transparent,
      iconTheme: iconTheme ?? IconThemeData(color: backColor ?? Colors.black),
      centerTitle: centerTitle,
      leading: showLeading
          ? (leading ??
              IconButton(
                icon: Icon(Icons.arrow_back_ios,
                    color: backColor ?? ThemeConfig.norBackColor),
                onPressed: backFunc ?? () => Navigator.of(context).pop(),
              ))
          : null,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(44.h);
}

class HPGradientScaffold extends StatelessWidget {
  final String? titleText;
  final Widget? title;
  final List<Widget>? actions;
  final bool? showGradient;
  final Widget body;
  final bool showLeading;
  final VoidCallback? backFunc;
  final Color? backIconColor;
  final SystemUiOverlayStyle? systemOverlayStyle;

  const HPGradientScaffold({
    Key? key,
    this.titleText,
    this.title,
    this.actions,
    this.showGradient,
    required this.body,
    this.showLeading = true,
    this.backFunc,
    this.backIconColor,
    this.systemOverlayStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.white,
      appBar: HPAppBar(
        text: titleText,
        title: title,
        actions: actions,
        backgroundColor: Colors.transparent,
        elevation: 0,
        backColor: backIconColor,
        showLeading: showLeading,
        backFunc: backFunc,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      body: SafeArea(
          child: Container(
        child: Stack(
          children: [
            showGradient == true
                ? Positioned(
                    child: Image.asset(R.assetsImagesAppIcon),
                    top: 0,
                    left: 0,
                    right: 0,
                    height: 200.h,
                  )
                : const SizedBox.shrink(),
            Positioned.fill(child: body)
          ],
        ),
      )),
    );
  }
}
