/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-20 15:54:23
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-20 17:22:55
 * @FilePath: /hwicc-mobile-flutter/lib/widgets/smart_refresher_widget.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:auapp/pub/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

/// 自定义统一下拉刷新头部样式
class HPRefreshHeader extends StatelessWidget {
  const HPRefreshHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return const WaterDropHeader(
      complete: Icon(Icons.done, color: Colors.green),
      failed: Icon(Icons.error, color: Colors.red),
      waterDropColor: ColorConstants.mainThemeColor,
    );
  }
}

/// 自定义统一上拉加载底部样式
class HPRefreshFooter extends StatelessWidget {
  const HPRefreshFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return ClassicFooter(
      loadingText: "refresh_loading".tr,
      noDataText: "refresh_no_data".tr,
      idleText: "refresh_load_more".tr,
      failedText: "refresh_load_failed".tr,
    );
  }
}
