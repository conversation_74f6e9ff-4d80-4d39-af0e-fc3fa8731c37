import 'package:auapp/utils/hp_date_util.dart';
import 'package:auapp/utils/hp_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';

//! 组件暂时有问题，当选择 ymd_hm,ymd_hms 不显示年
enum HPDatePickerType {
  y,
  ym,
  ymd,
  hm,
  hms,
  ymd_hm,
  ymd_hms,
}

class _YearPickerModel extends DatePickerModel {
  _YearPickerModel(DateTime currentTime, DateTime minTime, DateTime maxTime)
      : super(
          locale: HPDateTimePicker.getLocale(),
          minTime: minTime,
          maxTime: maxTime,
          currentTime: currentTime,
        );

  @override
  List<int> layoutProportions() => [1, 0, 0];

  @override
  DateTime finalTime() => DateTime(currentTime.year);
}

class _YearMonthPickerModel extends DatePickerModel {
  _YearMonthPickerModel(
      DateTime currentTime, DateTime minTime, DateTime maxTime)
      : super(
          locale: HPDateTimePicker.getLocale(),
          minTime: minTime,
          maxTime: maxTime,
          currentTime: currentTime,
        );

  @override
  List<int> layoutProportions() => [2, 1, 0];

  @override
  DateTime finalTime() => DateTime(currentTime.year, currentTime.month);
}

class HPDateTimePicker {
  static Future<HPDatePickerModel?> show({
    required BuildContext context,
    HPDatePickerType pickerType = HPDatePickerType.ymd_hms,
    DateTime? initialDate,
    DateTime? minDate,
    DateTime? maxDate,
    String dateFormat = "yyyy-MM-dd HH:mm:ss",
    bool returnMap = false,
  }) async {
    initialDate ??= DateTime.now();
    minDate ??= DateTime(2025, 1, 1, 0, 0, 0);
    maxDate ??= DateTime(2035, 12, 31, 23, 59, 59);

    switch (pickerType) {
      case HPDatePickerType.y:
        return _showCustomPicker(
          context,
          initialDate,
          minDate,
          maxDate,
          returnMap,
          dateFormat,
          pickerType,
        );

      case HPDatePickerType.ym:
        return _showCustomPicker(
          context,
          initialDate,
          minDate,
          maxDate,
          returnMap,
          dateFormat,
          pickerType,
        );

      case HPDatePickerType.ymd:
        return _showDatePicker(
          context,
          showTitleActions: true,
          initialDate: initialDate,
          minDate: minDate,
          maxDate: maxDate,
        );

      case HPDatePickerType.hm:
        return _showTimePicker(
          context,
          showTitleActions: true,
          showSecondsColumn: false,
          initialDate: initialDate,
        );

      case HPDatePickerType.hms:
        return _showTimePicker(
          context,
          showTitleActions: true,
          showSecondsColumn: true,
          initialDate: initialDate,
        );

      case HPDatePickerType.ymd_hm:
        return _showDatePicker(
          context,
          showTitleActions: true,
          initialDate: initialDate,
          minDate: minDate,
          maxDate: maxDate,
          dateFormat: dateFormat,
        );

      case HPDatePickerType.ymd_hms:
        return _showDatePicker(
          context,
          showTitleActions: true,
          initialDate: initialDate,
          minDate: minDate,
          maxDate: maxDate,
          dateFormat: dateFormat,
        );
    }
  }

  static Future<HPDatePickerModel?> _showDatePicker(
    BuildContext context, {
    DateTime? initialDate,
    DateTime? minDate,
    DateTime? maxDate,
    String dateFormat = "yyyy-MM-dd HH:mm:ss",
    bool showTitleActions = true,
  }) async {
    initialDate ??= DateTime.now();
    minDate ??= DateTime(2025, 1, 1, 0, 0, 0);
    maxDate ??= DateTime(2035, 12, 31, 23, 59, 59);

    DateTime? date = await DatePicker.showDatePicker(
      context,
      showTitleActions: showTitleActions,
      currentTime: initialDate,
      minTime: minDate,
      maxTime: maxDate,
    );

    return _handleReturn(date, dateFormat);
  }

  static Future<HPDatePickerModel?> _showTimePicker(
    BuildContext context, {
    DateTime? initialDate,
    bool showSecondsColumn = true,
    bool showTitleActions = true,
  }) async {
    initialDate ??= DateTime.now();

    DateTime? date = await DatePicker.showTimePicker(
      context,
      showTitleActions: showTitleActions,
      showSecondsColumn: showSecondsColumn,
      currentTime: initialDate,
    );

    return _handleReturn(date, "HH:mm:ss");
  }

  static Future<HPDatePickerModel?> _showCustomPicker(
    BuildContext context,
    DateTime initialDate,
    DateTime minDate,
    DateTime maxDate,
    bool returnMap,
    String dateFormat,
    HPDatePickerType pickerType,
  ) async {
    DateTime? date = await DatePicker.showPicker(
      context,
      showTitleActions: true,
      locale: HPDateTimePicker.getLocale(),
      pickerModel: pickerType == HPDatePickerType.y
          ? _YearPickerModel(initialDate, minDate, maxDate)
          : _YearMonthPickerModel(initialDate, minDate, maxDate),
    );

    return _handleReturn(date, dateFormat);
  }

  static HPDatePickerModel? _handleReturn(DateTime? date, String dateFormat) {
    if (date == null) {
      return null;
    }
    return HPDatePickerModel(
      dateStr: HPDateUtil.dateFormat(date, format: dateFormat),
      dateTime: date,
    );
  }

  static LocaleType getLocale() {
    switch (HPLocalization.getLocale()) {
      case "en-US":
        return LocaleType.en;
      case "zh-CN":
        return LocaleType.zh;
      case "zh-HK":
        return LocaleType.zh;
      default:
        return LocaleType.en;
    }
  }
}

class HPDatePickerModel {
  String dateStr;
  DateTime dateTime;

  HPDatePickerModel({
    required this.dateStr,
    required this.dateTime,
  });
}
