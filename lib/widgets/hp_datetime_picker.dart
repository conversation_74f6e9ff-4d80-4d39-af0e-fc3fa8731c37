import 'package:auapp/utils/hp_date_util.dart';
import 'package:auapp/utils/hp_localization.dart';
import 'package:flutter/material.dart';

enum HPDatePickerType {
  y,
  ym,
  ymd,
  hm,
  hms,
  ymd_hm,
  ymd_hms,
}

class HPDatePickerModel {
  final String dateStr;
  final DateTime dateTime;

  HPDatePickerModel({required this.dateStr, required this.dateTime});
}

class HPDateTimePicker {
  static Future<HPDatePickerModel?> show({
    required BuildContext context,
    HPDatePickerType pickerType = HPDatePickerType.ymd_hms,
    DateTime? initialDate,
    DateTime? minDate,
    DateTime? maxDate,
    String dateFormat = "yyyy-MM-dd HH:mm:ss",
    String? locale,
  }) async {
    initialDate ??= DateTime.now();
    minDate ??= DateTime(2020, 1, 1, 0, 0, 0);
    maxDate ??= DateTime(2030, 12, 31, 23, 59, 59);

    // 创建标题行
    List<String> titles = [];

    // 根据不同类型配置选择器数据和标题
    switch (pickerType) {
      case HPDatePickerType.ymd_hms:
        titles = ['年', '月', '日', '时', '分', '秒'];
        return _showCustomDateTimePicker(context, initialDate, minDate, maxDate,
            titles, dateFormat, pickerType);
      case HPDatePickerType.ymd_hm:
        titles = ['年', '月', '日', '时', '分'];
        return _showCustomDateTimePicker(context, initialDate, minDate, maxDate,
            titles, dateFormat, pickerType);
      case HPDatePickerType.ymd:
        titles = ['年', '月', '日'];
        return _showCustomDateTimePicker(context, initialDate, minDate, maxDate,
            titles, dateFormat, pickerType);
      case HPDatePickerType.ym:
        titles = ['年', '月'];
        return _showCustomDateTimePicker(context, initialDate, minDate, maxDate,
            titles, dateFormat, pickerType);
      case HPDatePickerType.y:
        titles = ['年'];
        return _showCustomDateTimePicker(context, initialDate, minDate, maxDate,
            titles, dateFormat, pickerType);
      case HPDatePickerType.hms:
        titles = ['时', '分', '秒'];
        return _showCustomTimePicker(
            context, initialDate, titles, dateFormat, pickerType);
      case HPDatePickerType.hm:
        titles = ['时', '分'];
        return _showCustomTimePicker(
            context, initialDate, titles, dateFormat, pickerType);
    }
  }

  static String getLocale() {
    return HPLocalization.getLocale();
  }

  // 自定义日期时间选择器
  static Future<HPDatePickerModel?> _showCustomDateTimePicker(
    BuildContext context,
    DateTime initialDate,
    DateTime minDate,
    DateTime maxDate,
    List<String> titles,
    String dateFormat,
    HPDatePickerType pickerType,
  ) async {
    // 创建数据
    List<List<String>> pickerData = [];
    List<int> selecteds = [];
    List<FixedExtentScrollController> controllers = [];

    // 年份数据
    if (titles.contains('年')) {
      List<String> years = [];
      for (int i = minDate.year; i <= maxDate.year; i++) {
        years.add(i.toString());
      }
      pickerData.add(years);
      int initialYearIndex = years.indexOf(initialDate.year.toString());
      selecteds.add(initialYearIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialYearIndex));
    }

    // 月份数据
    if (titles.contains('月')) {
      List<String> months = [];
      for (int i = 1; i <= 12; i++) {
        months.add(i.toString());
      }
      pickerData.add(months);
      int initialMonthIndex = initialDate.month - 1;
      selecteds.add(initialMonthIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialMonthIndex));
    }

    // 日期数据
    if (titles.contains('日')) {
      List<String> days = [];
      int daysInMonth =
          DateTime(initialDate.year, initialDate.month + 1, 0).day;
      for (int i = 1; i <= daysInMonth; i++) {
        days.add(i.toString());
      }
      pickerData.add(days);
      int initialDayIndex = initialDate.day - 1;
      selecteds.add(initialDayIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialDayIndex));
    }

    // 小时数据
    if (titles.contains('时')) {
      List<String> hours = [];
      for (int i = 0; i <= 23; i++) {
        hours.add(i.toString().padLeft(2, '0'));
      }
      pickerData.add(hours);
      int initialHourIndex = initialDate.hour;
      selecteds.add(initialHourIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialHourIndex));
    }

    // 分钟数据
    if (titles.contains('分')) {
      List<String> minutes = [];
      for (int i = 0; i <= 59; i++) {
        minutes.add(i.toString().padLeft(2, '0'));
      }
      pickerData.add(minutes);
      int initialMinuteIndex = initialDate.minute;
      selecteds.add(initialMinuteIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialMinuteIndex));
    }

    // 秒数据
    if (titles.contains('秒') &&
        (pickerType == HPDatePickerType.ymd_hms ||
            pickerType == HPDatePickerType.hms)) {
      List<String> seconds = [];
      for (int i = 0; i <= 59; i++) {
        seconds.add(i.toString().padLeft(2, '0'));
      }
      pickerData.add(seconds);
      int initialSecondIndex = initialDate.second;
      selecteds.add(initialSecondIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialSecondIndex));
    }

    DateTime? selectedDateTime;

    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          return Container(
            height: 400,
            padding: const EdgeInsets.only(top: 16),
            child: Column(
              children: [
                // 标题栏
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        '请选择时间',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // 标题行
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: titles
                        .map((title) => Expanded(
                              child: Center(
                                child: Text(
                                  title,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                            ))
                        .toList(),
                  ),
                ),

                // 选择器
                Expanded(
                  child: Stack(
                    children: [
                      // 中间选中项的蓝色背景
                      Positioned.fill(
                        child: Center(
                          child: Container(
                            height: 40,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.blue.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),

                      // 滚动选择器
                      Row(
                        children: List.generate(
                          pickerData.length,
                          (columnIndex) => Expanded(
                            child: ListWheelScrollView.useDelegate(
                              controller: controllers[columnIndex],
                              itemExtent: 40,
                              perspective: 0.005,
                              diameterRatio: 1.5,
                              physics: const FixedExtentScrollPhysics(),
                              onSelectedItemChanged: (index) {
                                selecteds[columnIndex] = index;

                                // 如果改变了年或月，需要更新日期数据
                                if ((columnIndex == 0 && titles[0] == '年') ||
                                    (columnIndex == 1 && titles[1] == '月')) {
                                  if (titles.contains('日')) {
                                    int yearIndex = titles.indexOf('年');
                                    int monthIndex = titles.indexOf('月');
                                    int dayIndex = titles.indexOf('日');

                                    int year = yearIndex >= 0
                                        ? int.parse(pickerData[yearIndex]
                                            [selecteds[yearIndex]])
                                        : initialDate.year;

                                    int month = monthIndex >= 0
                                        ? int.parse(pickerData[monthIndex]
                                            [selecteds[monthIndex]])
                                        : initialDate.month;

                                    int daysInMonth =
                                        DateTime(year, month + 1, 0).day;

                                    List<String> newDays = [];
                                    for (int i = 1; i <= daysInMonth; i++) {
                                      newDays.add(i.toString());
                                    }

                                    setState(() {
                                      pickerData[dayIndex] = newDays;
                                      if (selecteds[dayIndex] >=
                                          newDays.length) {
                                        selecteds[dayIndex] =
                                            newDays.length - 1;
                                        controllers[dayIndex]
                                            .jumpToItem(selecteds[dayIndex]);
                                      }
                                    });
                                  }
                                }
                              },
                              childDelegate: ListWheelChildBuilderDelegate(
                                childCount: pickerData[columnIndex].length,
                                builder: (context, index) {
                                  return Container(
                                    alignment: Alignment.center,
                                    child: Text(
                                      pickerData[columnIndex][index],
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: selecteds[columnIndex] == index
                                            ? Colors.blue
                                            : Colors.black,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 确定按钮
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: ElevatedButton(
                    onPressed: () {
                      // 构建选中的日期时间
                      int year = initialDate.year;
                      int month = initialDate.month;
                      int day = initialDate.day;
                      int hour = initialDate.hour;
                      int minute = initialDate.minute;
                      int second = initialDate.second;

                      if (titles.contains('年')) {
                        int yearIndex = titles.indexOf('年');
                        year = int.parse(
                            pickerData[yearIndex][selecteds[yearIndex]]);
                      }

                      if (titles.contains('月')) {
                        int monthIndex = titles.indexOf('月');
                        month = int.parse(
                            pickerData[monthIndex][selecteds[monthIndex]]);
                      }

                      if (titles.contains('日')) {
                        int dayIndex = titles.indexOf('日');
                        day = int.parse(
                            pickerData[dayIndex][selecteds[dayIndex]]);
                      }

                      if (titles.contains('时')) {
                        int hourIndex = titles.indexOf('时');
                        hour = int.parse(
                            pickerData[hourIndex][selecteds[hourIndex]]);
                      }

                      if (titles.contains('分')) {
                        int minuteIndex = titles.indexOf('分');
                        minute = int.parse(
                            pickerData[minuteIndex][selecteds[minuteIndex]]);
                      }

                      if (titles.contains('秒') &&
                          (pickerType == HPDatePickerType.ymd_hms ||
                              pickerType == HPDatePickerType.hms)) {
                        int secondIndex = titles.indexOf('秒');
                        second = int.parse(
                            pickerData[secondIndex][selecteds[secondIndex]]);
                      }

                      selectedDateTime =
                          DateTime(year, month, day, hour, minute, second);
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF29B6F6),
                      minimumSize: const Size(double.infinity, 50),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      '确定',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),

                // 底部安全区域指示器
                Container(
                  height: 4,
                  width: 40,
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ],
            ),
          );
        });
      },
    );

    if (selectedDateTime != null) {
      return HPDatePickerModel(
        dateStr: HPDateUtil.dateFormat(selectedDateTime!, format: dateFormat),
        dateTime: selectedDateTime!,
      );
    }

    return null;
  }

  // 自定义时间选择器
  static Future<HPDatePickerModel?> _showCustomTimePicker(
    BuildContext context,
    DateTime initialDate,
    List<String> titles,
    String dateFormat,
    HPDatePickerType pickerType,
  ) async {
    // 创建数据
    List<List<String>> pickerData = [];
    List<int> selecteds = [];
    List<FixedExtentScrollController> controllers = [];

    // 小时数据
    if (titles.contains('时')) {
      List<String> hours = [];
      for (int i = 0; i <= 23; i++) {
        hours.add(i.toString().padLeft(2, '0'));
      }
      pickerData.add(hours);
      int initialHourIndex = initialDate.hour;
      selecteds.add(initialHourIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialHourIndex));
    }

    // 分钟数据
    if (titles.contains('分')) {
      List<String> minutes = [];
      for (int i = 0; i <= 59; i++) {
        minutes.add(i.toString().padLeft(2, '0'));
      }
      pickerData.add(minutes);
      int initialMinuteIndex = initialDate.minute;
      selecteds.add(initialMinuteIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialMinuteIndex));
    }

    // 秒数据
    if (titles.contains('秒') && pickerType == HPDatePickerType.hms) {
      List<String> seconds = [];
      for (int i = 0; i <= 59; i++) {
        seconds.add(i.toString().padLeft(2, '0'));
      }
      pickerData.add(seconds);
      int initialSecondIndex = initialDate.second;
      selecteds.add(initialSecondIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialSecondIndex));
    }

    DateTime? selectedDateTime;

    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      builder: (BuildContext context) {
        return Container(
          height: 400,
          padding: const EdgeInsets.only(top: 16),
          child: Column(
            children: [
              // 标题栏
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '请选择时间',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // 标题行
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: titles
                      .map((title) => Expanded(
                            child: Center(
                              child: Text(
                                title,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ))
                      .toList(),
                ),
              ),

              // 选择器
              Expanded(
                child: Stack(
                  children: [
                    // 中间选中项的蓝色背景
                    Positioned.fill(
                      child: Center(
                        child: Container(
                          height: 40,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.blue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),

                    // 滚动选择器
                    Row(
                      children: List.generate(
                        pickerData.length,
                        (columnIndex) => Expanded(
                          child: ListWheelScrollView.useDelegate(
                            controller: controllers[columnIndex],
                            itemExtent: 40,
                            perspective: 0.005,
                            diameterRatio: 1.5,
                            physics: const FixedExtentScrollPhysics(),
                            onSelectedItemChanged: (index) {
                              selecteds[columnIndex] = index;
                            },
                            childDelegate: ListWheelChildBuilderDelegate(
                              childCount: pickerData[columnIndex].length,
                              builder: (context, index) {
                                return Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    pickerData[columnIndex][index],
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: selecteds[columnIndex] == index
                                          ? Colors.blue
                                          : Colors.black,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 确定按钮
              Padding(
                padding: const EdgeInsets.all(16),
                child: ElevatedButton(
                  onPressed: () {
                    // 构建选中的时间
                    int hour = initialDate.hour;
                    int minute = initialDate.minute;
                    int second = initialDate.second;

                    if (titles.contains('时')) {
                      int hourIndex = titles.indexOf('时');
                      hour = int.parse(
                          pickerData[hourIndex][selecteds[hourIndex]]);
                    }

                    if (titles.contains('分')) {
                      int minuteIndex = titles.indexOf('分');
                      minute = int.parse(
                          pickerData[minuteIndex][selecteds[minuteIndex]]);
                    }

                    if (titles.contains('秒') &&
                        pickerType == HPDatePickerType.hms) {
                      int secondIndex = titles.indexOf('秒');
                      second = int.parse(
                          pickerData[secondIndex][selecteds[secondIndex]]);
                    }

                    selectedDateTime = DateTime(
                        initialDate.year,
                        initialDate.month,
                        initialDate.day,
                        hour,
                        minute,
                        second);
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    minimumSize: const Size(double.infinity, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    '确定',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),

              // 底部安全区域指示器
              Container(
                height: 4,
                width: 40,
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ],
          ),
        );
      },
    );

    if (selectedDateTime != null) {
      return HPDatePickerModel(
        dateStr: HPDateUtil.dateFormat(selectedDateTime!, format: dateFormat),
        dateTime: selectedDateTime!,
      );
    }

    return null;
  }
}
