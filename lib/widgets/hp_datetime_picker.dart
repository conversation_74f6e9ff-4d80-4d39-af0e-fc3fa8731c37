import 'package:auapp/utils/hp_date_util.dart';
import 'package:auapp/utils/hp_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

enum HPDatePickerType {
  y,
  ym,
  ymd,
  hm,
  hms,
  ymd_hm,
  ymd_hms,
}

class HPDatePickerModel {
  final String dateStr;
  final DateTime dateTime;

  HPDatePickerModel({required this.dateStr, required this.dateTime});
}

class HPDateTimePicker {
  static Future<HPDatePickerModel?> show({
    required BuildContext context,
    HPDatePickerType pickerType = HPDatePickerType.ymd_hms,
    DateTime? initialDate,
    DateTime? minDate,
    DateTime? maxDate,
    String dateFormat = "yyyy-MM-dd HH:mm:ss",
    String? locale, //! 该字段暂时没用，使用的当前默认语言
  }) async {
    initialDate ??= DateTime.now();
    minDate ??= DateTime(2020, 1, 1, 0, 0, 0);
    maxDate ??= DateTime(2030, 12, 31, 23, 59, 59);

    // 创建标题行
    List<String> titles = [];

    // 根据不同类型配置选择器数据和标题
    switch (pickerType) {
      case HPDatePickerType.ymd_hms:
        titles = [
          "transaction_year".tr,
          "transaction_month".tr,
          "transaction_day".tr,
          "transaction_hour".tr,
          "transaction_minute".tr,
          "transaction_second".tr
        ];
        return _showCustomDateTimePicker(context, initialDate, minDate, maxDate,
            titles, dateFormat, pickerType, locale);
      case HPDatePickerType.ymd_hm:
        titles = [
          "transaction_year".tr,
          "transaction_month".tr,
          "transaction_day".tr,
          "transaction_hour".tr,
          "transaction_minute".tr
        ];
        return _showCustomDateTimePicker(context, initialDate, minDate, maxDate,
            titles, dateFormat, pickerType, locale);
      case HPDatePickerType.ymd:
        titles = [
          "transaction_year".tr,
          "transaction_month".tr,
          "transaction_day".tr
        ];
        return _showCustomDateTimePicker(context, initialDate, minDate, maxDate,
            titles, dateFormat, pickerType, locale);
      case HPDatePickerType.ym:
        titles = ["transaction_year".tr, "transaction_month".tr];
        return _showCustomDateTimePicker(context, initialDate, minDate, maxDate,
            titles, dateFormat, pickerType, locale);
      case HPDatePickerType.y:
        titles = ["transaction_year".tr];
        return _showCustomDateTimePicker(context, initialDate, minDate, maxDate,
            titles, dateFormat, pickerType, locale);
      case HPDatePickerType.hms:
        titles = [
          "transaction_hour".tr,
          "transaction_minute".tr,
          "transaction_second".tr
        ];
        return _showCustomTimePicker(
            context, initialDate, titles, dateFormat, pickerType, locale);
      case HPDatePickerType.hm:
        titles = ["transaction_hour".tr, "transaction_minute".tr];
        return _showCustomTimePicker(
            context, initialDate, titles, dateFormat, pickerType, locale);
    }
  }

  static String getLocale() {
    return HPLocalization.getLocale();
  }

  /// 更新数据范围的辅助方法
  static void _updateDataRanges({
    required List<List<String>> pickerData,
    required List<int> selecteds,
    required List<FixedExtentScrollController> controllers,
    required HPDatePickerType pickerType,
    required DateTime minDate,
    required DateTime maxDate,
    required bool hasYear,
    required bool hasMonth,
    required bool hasDay,
    required bool hasHour,
    required bool hasMinute,
    required bool hasSecond,
    required Function setState,
  }) {
    int currentIndex = 0;

    // 获取当前选择的值
    int currentYear = minDate.year;
    int currentMonth = minDate.month;
    int currentDay = minDate.day;
    int currentHour = minDate.hour;
    int currentMinute = minDate.minute;

    if (hasYear && currentIndex < pickerData.length) {
      currentYear =
          int.parse(pickerData[currentIndex][selecteds[currentIndex]]);
      currentIndex++;
    }
    if (hasMonth && currentIndex < pickerData.length) {
      int monthOffset = (currentYear == minDate.year) ? minDate.month : 1;
      currentMonth =
          int.parse(pickerData[currentIndex][selecteds[currentIndex]]) +
              monthOffset -
              1;
      currentIndex++;
    }
    if (hasDay && currentIndex < pickerData.length) {
      int dayOffset = 1;
      if (currentYear == minDate.year && currentMonth == minDate.month) {
        dayOffset = minDate.day;
      }
      currentDay =
          int.parse(pickerData[currentIndex][selecteds[currentIndex]]) +
              dayOffset -
              1;
      currentIndex++;
    }
    if (hasHour && currentIndex < pickerData.length) {
      int hourOffset = 0;
      if (currentYear == minDate.year &&
          currentMonth == minDate.month &&
          currentDay == minDate.day) {
        hourOffset = minDate.hour;
      }
      currentHour =
          int.parse(pickerData[currentIndex][selecteds[currentIndex]]) +
              hourOffset;
      currentIndex++;
    }
    if (hasMinute && currentIndex < pickerData.length) {
      int minuteOffset = 0;
      if (currentYear == minDate.year &&
          currentMonth == minDate.month &&
          currentDay == minDate.day &&
          currentHour == minDate.hour) {
        minuteOffset = minDate.minute;
      }
      currentMinute =
          int.parse(pickerData[currentIndex][selecteds[currentIndex]]) +
              minuteOffset;
      currentIndex++;
    }

    // 重新生成需要更新的数据
    currentIndex = 0;

    // 跳过年份
    if (hasYear) currentIndex++;

    // 更新月份数据
    if (hasMonth) {
      int startMonth = (currentYear == minDate.year) ? minDate.month : 1;
      int endMonth = (currentYear == maxDate.year) ? maxDate.month : 12;

      List<String> newMonths = [];
      for (int i = startMonth; i <= endMonth; i++) {
        newMonths.add(i.toString());
      }

      if (pickerData[currentIndex].length != newMonths.length) {
        setState(() {
          pickerData[currentIndex] = newMonths;
          if (selecteds[currentIndex] >= newMonths.length) {
            selecteds[currentIndex] = newMonths.length - 1;
            controllers[currentIndex].jumpToItem(selecteds[currentIndex]);
          }
        });
      }
      currentIndex++;
    }

    // 更新日期数据
    if (hasDay) {
      int startDay = 1;
      int endDay = DateTime(currentYear, currentMonth + 1, 0).day;

      if (currentYear == minDate.year && currentMonth == minDate.month) {
        startDay = minDate.day;
      }
      if (currentYear == maxDate.year && currentMonth == maxDate.month) {
        endDay = maxDate.day;
      }

      List<String> newDays = [];
      for (int i = startDay; i <= endDay; i++) {
        newDays.add(i.toString());
      }

      setState(() {
        pickerData[currentIndex] = newDays;
        if (selecteds[currentIndex] >= newDays.length) {
          selecteds[currentIndex] = newDays.length - 1;
          controllers[currentIndex].jumpToItem(selecteds[currentIndex]);
        }
      });
      currentIndex++;
    }

    // 更新小时数据
    if (hasHour) {
      int startHour = 0;
      int endHour = 23;

      if (currentYear == minDate.year &&
          currentMonth == minDate.month &&
          currentDay == minDate.day) {
        startHour = minDate.hour;
      }
      if (currentYear == maxDate.year &&
          currentMonth == maxDate.month &&
          currentDay == maxDate.day) {
        endHour = maxDate.hour;
      }

      List<String> newHours = [];
      for (int i = startHour; i <= endHour; i++) {
        newHours.add(i.toString().padLeft(2, '0'));
      }

      if (pickerData[currentIndex].length != newHours.length) {
        setState(() {
          pickerData[currentIndex] = newHours;
          if (selecteds[currentIndex] >= newHours.length) {
            selecteds[currentIndex] = newHours.length - 1;
            controllers[currentIndex].jumpToItem(selecteds[currentIndex]);
          }
        });
      }
      currentIndex++;
    }

    // 更新分钟数据
    if (hasMinute) {
      int startMinute = 0;
      int endMinute = 59;

      if (currentYear == minDate.year &&
          currentMonth == minDate.month &&
          currentDay == minDate.day &&
          currentHour == minDate.hour) {
        startMinute = minDate.minute;
      }
      if (currentYear == maxDate.year &&
          currentMonth == maxDate.month &&
          currentDay == maxDate.day &&
          currentHour == maxDate.hour) {
        endMinute = maxDate.minute;
      }

      List<String> newMinutes = [];
      for (int i = startMinute; i <= endMinute; i++) {
        newMinutes.add(i.toString().padLeft(2, '0'));
      }

      if (pickerData[currentIndex].length != newMinutes.length) {
        setState(() {
          pickerData[currentIndex] = newMinutes;
          if (selecteds[currentIndex] >= newMinutes.length) {
            selecteds[currentIndex] = newMinutes.length - 1;
            controllers[currentIndex].jumpToItem(selecteds[currentIndex]);
          }
        });
      }
      currentIndex++;
    }

    // 更新秒数据
    if (hasSecond) {
      int startSecond = 0;
      int endSecond = 59;

      if (currentYear == minDate.year &&
          currentMonth == minDate.month &&
          currentDay == minDate.day &&
          currentHour == minDate.hour &&
          currentMinute == minDate.minute) {
        startSecond = minDate.second;
      }
      if (currentYear == maxDate.year &&
          currentMonth == maxDate.month &&
          currentDay == maxDate.day &&
          currentHour == maxDate.hour &&
          currentMinute == maxDate.minute) {
        endSecond = maxDate.second;
      }

      List<String> newSeconds = [];
      for (int i = startSecond; i <= endSecond; i++) {
        newSeconds.add(i.toString().padLeft(2, '0'));
      }

      if (pickerData[currentIndex].length != newSeconds.length) {
        setState(() {
          pickerData[currentIndex] = newSeconds;
          if (selecteds[currentIndex] >= newSeconds.length) {
            selecteds[currentIndex] = newSeconds.length - 1;
            controllers[currentIndex].jumpToItem(selecteds[currentIndex]);
          }
        });
      }
    }
  }

  // 自定义日期时间选择器
  static Future<HPDatePickerModel?> _showCustomDateTimePicker(
    BuildContext context,
    DateTime initialDate,
    DateTime minDate,
    DateTime maxDate,
    List<String> titles,
    String dateFormat,
    HPDatePickerType pickerType,
    String? locale,
  ) async {
    // 创建数据
    List<List<String>> pickerData = [];
    List<int> selecteds = [];
    List<FixedExtentScrollController> controllers = [];

    // 根据 pickerType 确定需要的数据列
    bool hasYear = pickerType == HPDatePickerType.y ||
        pickerType == HPDatePickerType.ym ||
        pickerType == HPDatePickerType.ymd ||
        pickerType == HPDatePickerType.ymd_hm ||
        pickerType == HPDatePickerType.ymd_hms;
    bool hasMonth = pickerType == HPDatePickerType.ym ||
        pickerType == HPDatePickerType.ymd ||
        pickerType == HPDatePickerType.ymd_hm ||
        pickerType == HPDatePickerType.ymd_hms;
    bool hasDay = pickerType == HPDatePickerType.ymd ||
        pickerType == HPDatePickerType.ymd_hm ||
        pickerType == HPDatePickerType.ymd_hms;
    bool hasHour = pickerType == HPDatePickerType.hm ||
        pickerType == HPDatePickerType.hms ||
        pickerType == HPDatePickerType.ymd_hm ||
        pickerType == HPDatePickerType.ymd_hms;
    bool hasMinute = pickerType == HPDatePickerType.hm ||
        pickerType == HPDatePickerType.hms ||
        pickerType == HPDatePickerType.ymd_hm ||
        pickerType == HPDatePickerType.ymd_hms;
    bool hasSecond = pickerType == HPDatePickerType.hms ||
        pickerType == HPDatePickerType.ymd_hms;

    // 年份数据
    if (hasYear) {
      List<String> years = [];
      for (int i = minDate.year; i <= maxDate.year; i++) {
        years.add(i.toString());
      }
      pickerData.add(years);
      int initialYearIndex = years.indexOf(initialDate.year.toString());
      selecteds.add(initialYearIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialYearIndex));
    }

    // 月份数据
    if (hasMonth) {
      List<String> months = [];
      int currentYear = hasYear ? initialDate.year : minDate.year;
      int startMonth = (currentYear == minDate.year) ? minDate.month : 1;
      int endMonth = (currentYear == maxDate.year) ? maxDate.month : 12;

      for (int i = startMonth; i <= endMonth; i++) {
        months.add(i.toString());
      }
      pickerData.add(months);
      int initialMonthIndex = initialDate.month - startMonth;
      if (initialMonthIndex < 0) initialMonthIndex = 0;
      if (initialMonthIndex >= months.length) {
        initialMonthIndex = months.length - 1;
      }
      selecteds.add(initialMonthIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialMonthIndex));
    }

    // 日期数据
    if (hasDay) {
      List<String> days = [];
      int currentYear = hasYear ? initialDate.year : minDate.year;
      int currentMonth = hasMonth ? initialDate.month : minDate.month;

      int startDay = 1;
      int endDay = DateTime(currentYear, currentMonth + 1, 0).day;

      // 如果是最小日期的年月，限制开始日期
      if (currentYear == minDate.year && currentMonth == minDate.month) {
        startDay = minDate.day;
      }

      // 如果是最大日期的年月，限制结束日期
      if (currentYear == maxDate.year && currentMonth == maxDate.month) {
        endDay = maxDate.day;
      }

      for (int i = startDay; i <= endDay; i++) {
        days.add(i.toString());
      }
      pickerData.add(days);
      int initialDayIndex = initialDate.day - startDay;
      if (initialDayIndex < 0) initialDayIndex = 0;
      if (initialDayIndex >= days.length) {
        initialDayIndex = days.length - 1;
      }
      selecteds.add(initialDayIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialDayIndex));
    }

    // 小时数据
    if (hasHour) {
      List<String> hours = [];
      int currentYear = hasYear ? initialDate.year : minDate.year;
      int currentMonth = hasMonth ? initialDate.month : minDate.month;
      int currentDay = hasDay ? initialDate.day : minDate.day;

      int startHour = 0;
      int endHour = 23;

      // 如果是最小日期，限制开始小时
      if (currentYear == minDate.year &&
          currentMonth == minDate.month &&
          currentDay == minDate.day) {
        startHour = minDate.hour;
      }

      // 如果是最大日期，限制结束小时
      if (currentYear == maxDate.year &&
          currentMonth == maxDate.month &&
          currentDay == maxDate.day) {
        endHour = maxDate.hour;
      }

      for (int i = startHour; i <= endHour; i++) {
        hours.add(i.toString().padLeft(2, '0'));
      }
      pickerData.add(hours);
      int initialHourIndex = initialDate.hour - startHour;
      if (initialHourIndex < 0) initialHourIndex = 0;
      if (initialHourIndex >= hours.length) {
        initialHourIndex = hours.length - 1;
      }
      selecteds.add(initialHourIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialHourIndex));
    }

    // 分钟数据
    if (hasMinute) {
      List<String> minutes = [];
      int currentYear = hasYear ? initialDate.year : minDate.year;
      int currentMonth = hasMonth ? initialDate.month : minDate.month;
      int currentDay = hasDay ? initialDate.day : minDate.day;
      int currentHour = hasHour ? initialDate.hour : minDate.hour;

      int startMinute = 0;
      int endMinute = 59;

      // 如果是最小日期时间，限制开始分钟
      if (currentYear == minDate.year &&
          currentMonth == minDate.month &&
          currentDay == minDate.day &&
          currentHour == minDate.hour) {
        startMinute = minDate.minute;
      }

      // 如果是最大日期时间，限制结束分钟
      if (currentYear == maxDate.year &&
          currentMonth == maxDate.month &&
          currentDay == maxDate.day &&
          currentHour == maxDate.hour) {
        endMinute = maxDate.minute;
      }

      for (int i = startMinute; i <= endMinute; i++) {
        minutes.add(i.toString().padLeft(2, '0'));
      }
      pickerData.add(minutes);
      int initialMinuteIndex = initialDate.minute - startMinute;
      if (initialMinuteIndex < 0) initialMinuteIndex = 0;
      if (initialMinuteIndex >= minutes.length) {
        initialMinuteIndex = minutes.length - 1;
      }
      selecteds.add(initialMinuteIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialMinuteIndex));
    }

    // 秒数据
    if (hasSecond) {
      List<String> seconds = [];
      int currentYear = hasYear ? initialDate.year : minDate.year;
      int currentMonth = hasMonth ? initialDate.month : minDate.month;
      int currentDay = hasDay ? initialDate.day : minDate.day;
      int currentHour = hasHour ? initialDate.hour : minDate.hour;
      int currentMinute = hasMinute ? initialDate.minute : minDate.minute;

      int startSecond = 0;
      int endSecond = 59;

      // 如果是最小日期时间，限制开始秒
      if (currentYear == minDate.year &&
          currentMonth == minDate.month &&
          currentDay == minDate.day &&
          currentHour == minDate.hour &&
          currentMinute == minDate.minute) {
        startSecond = minDate.second;
      }

      // 如果是最大日期时间，限制结束秒
      if (currentYear == maxDate.year &&
          currentMonth == maxDate.month &&
          currentDay == maxDate.day &&
          currentHour == maxDate.hour &&
          currentMinute == maxDate.minute) {
        endSecond = maxDate.second;
      }

      for (int i = startSecond; i <= endSecond; i++) {
        seconds.add(i.toString().padLeft(2, '0'));
      }
      pickerData.add(seconds);
      int initialSecondIndex = initialDate.second - startSecond;
      if (initialSecondIndex < 0) initialSecondIndex = 0;
      if (initialSecondIndex >= seconds.length) {
        initialSecondIndex = seconds.length - 1;
      }
      selecteds.add(initialSecondIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialSecondIndex));
    }

    DateTime? selectedDateTime;

    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          return SizedBox(
            height: 428.h,
            child: Column(
              children: [
                // 标题栏
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 1.sw - 56.w,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 16),
                        child: Text(
                          'transaction_select_time'.tr,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Icon(
                          Icons.close,
                          size: 24,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
                // 标题行
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: titles
                        .map((title) => Expanded(
                              child: Center(
                                child: Text(
                                  title,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                            ))
                        .toList(),
                  ),
                ),

                // 选择器
                Expanded(
                  child: Stack(
                    children: [
                      // 中间选中项的蓝色背景
                      Positioned.fill(
                        child: Center(
                          child: Container(
                            height: 40,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.blue.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),

                      // 滚动选择器
                      Row(
                        children: List.generate(
                          pickerData.length,
                          (columnIndex) => Expanded(
                            child: ListWheelScrollView.useDelegate(
                              controller: controllers[columnIndex],
                              itemExtent: 40,
                              perspective: 0.005,
                              diameterRatio: 1.5,
                              physics: const FixedExtentScrollPhysics(),
                              onSelectedItemChanged: (index) {
                                selecteds[columnIndex] = index;

                                // 使用辅助方法更新数据范围
                                _updateDataRanges(
                                  pickerData: pickerData,
                                  selecteds: selecteds,
                                  controllers: controllers,
                                  pickerType: pickerType,
                                  minDate: minDate,
                                  maxDate: maxDate,
                                  hasYear: hasYear,
                                  hasMonth: hasMonth,
                                  hasDay: hasDay,
                                  hasHour: hasHour,
                                  hasMinute: hasMinute,
                                  hasSecond: hasSecond,
                                  setState: setState,
                                );
                              },
                              childDelegate: ListWheelChildBuilderDelegate(
                                childCount: pickerData[columnIndex].length,
                                builder: (context, index) {
                                  return Container(
                                    alignment: Alignment.center,
                                    child: Text(
                                      pickerData[columnIndex][index],
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: selecteds[columnIndex] == index
                                            ? Colors.blue
                                            : Colors.black,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 确定按钮
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: ElevatedButton(
                    onPressed: () {
                      // 构建选中的日期时间
                      int year = initialDate.year;
                      int month = initialDate.month;
                      int day = initialDate.day;
                      int hour = initialDate.hour;
                      int minute = initialDate.minute;
                      int second = initialDate.second;

                      int currentIndex = 0;

                      if (hasYear) {
                        year = int.parse(
                            pickerData[currentIndex][selecteds[currentIndex]]);
                        currentIndex++;
                      }

                      if (hasMonth) {
                        int monthOffset =
                            (year == minDate.year) ? minDate.month : 1;
                        month = int.parse(pickerData[currentIndex]
                                [selecteds[currentIndex]]) +
                            monthOffset -
                            1;
                        currentIndex++;
                      }

                      if (hasDay) {
                        int dayOffset = 1;
                        if (year == minDate.year && month == minDate.month) {
                          dayOffset = minDate.day;
                        }
                        day = int.parse(pickerData[currentIndex]
                                [selecteds[currentIndex]]) +
                            dayOffset -
                            1;
                        currentIndex++;
                      }

                      if (hasHour) {
                        int hourOffset = 0;
                        if (year == minDate.year &&
                            month == minDate.month &&
                            day == minDate.day) {
                          hourOffset = minDate.hour;
                        }
                        hour = int.parse(pickerData[currentIndex]
                                [selecteds[currentIndex]]) +
                            hourOffset;
                        currentIndex++;
                      }

                      if (hasMinute) {
                        int minuteOffset = 0;
                        if (year == minDate.year &&
                            month == minDate.month &&
                            day == minDate.day &&
                            hour == minDate.hour) {
                          minuteOffset = minDate.minute;
                        }
                        minute = int.parse(pickerData[currentIndex]
                                [selecteds[currentIndex]]) +
                            minuteOffset;
                        currentIndex++;
                      }

                      if (hasSecond) {
                        int secondOffset = 0;
                        if (year == minDate.year &&
                            month == minDate.month &&
                            day == minDate.day &&
                            hour == minDate.hour &&
                            minute == minDate.minute) {
                          secondOffset = minDate.second;
                        }
                        second = int.parse(pickerData[currentIndex]
                                [selecteds[currentIndex]]) +
                            secondOffset;
                        currentIndex++;
                      }

                      selectedDateTime =
                          DateTime(year, month, day, hour, minute, second);
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF29B6F6),
                      minimumSize: const Size(double.infinity, 50),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'transaction_confirm'.tr,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),

                // 底部安全区域指示器
                Container(
                  height: 4,
                  width: 40,
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ],
            ),
          );
        });
      },
    );

    if (selectedDateTime != null) {
      return HPDatePickerModel(
        dateStr: HPDateUtil.dateFormat(selectedDateTime!, format: dateFormat),
        dateTime: selectedDateTime!,
      );
    }

    return null;
  }

  // 自定义时间选择器
  static Future<HPDatePickerModel?> _showCustomTimePicker(
    BuildContext context,
    DateTime initialDate,
    List<String> titles,
    String dateFormat,
    HPDatePickerType pickerType,
    String? locale,
  ) async {
    // 创建数据
    List<List<String>> pickerData = [];
    List<int> selecteds = [];
    List<FixedExtentScrollController> controllers = [];

    // 根据 pickerType 确定需要的数据列
    bool hasHour =
        pickerType == HPDatePickerType.hm || pickerType == HPDatePickerType.hms;
    bool hasMinute =
        pickerType == HPDatePickerType.hm || pickerType == HPDatePickerType.hms;
    bool hasSecond = pickerType == HPDatePickerType.hms;

    // 小时数据
    if (hasHour) {
      List<String> hours = [];
      for (int i = 0; i <= 23; i++) {
        hours.add(i.toString().padLeft(2, '0'));
      }
      pickerData.add(hours);
      int initialHourIndex = initialDate.hour;
      selecteds.add(initialHourIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialHourIndex));
    }

    // 分钟数据
    if (hasMinute) {
      List<String> minutes = [];
      for (int i = 0; i <= 59; i++) {
        minutes.add(i.toString().padLeft(2, '0'));
      }
      pickerData.add(minutes);
      int initialMinuteIndex = initialDate.minute;
      selecteds.add(initialMinuteIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialMinuteIndex));
    }

    // 秒数据
    if (hasSecond) {
      List<String> seconds = [];
      for (int i = 0; i <= 59; i++) {
        seconds.add(i.toString().padLeft(2, '0'));
      }
      pickerData.add(seconds);
      int initialSecondIndex = initialDate.second;
      selecteds.add(initialSecondIndex);
      controllers
          .add(FixedExtentScrollController(initialItem: initialSecondIndex));
    }

    DateTime? selectedDateTime;

    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      builder: (BuildContext context) {
        return Container(
          height: 400,
          padding: const EdgeInsets.only(top: 16),
          child: Column(
            children: [
              // 标题栏
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "transaction_select_time".tr,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // 标题行
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: titles
                      .map((title) => Expanded(
                            child: Center(
                              child: Text(
                                title,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ))
                      .toList(),
                ),
              ),

              // 选择器
              Expanded(
                child: Stack(
                  children: [
                    // 中间选中项的蓝色背景
                    Positioned.fill(
                      child: Center(
                        child: Container(
                          height: 40,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.blue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),

                    // 滚动选择器
                    Row(
                      children: List.generate(
                        pickerData.length,
                        (columnIndex) => Expanded(
                          child: ListWheelScrollView.useDelegate(
                            controller: controllers[columnIndex],
                            itemExtent: 40,
                            perspective: 0.005,
                            diameterRatio: 1.5,
                            physics: const FixedExtentScrollPhysics(),
                            onSelectedItemChanged: (index) {
                              selecteds[columnIndex] = index;
                            },
                            childDelegate: ListWheelChildBuilderDelegate(
                              childCount: pickerData[columnIndex].length,
                              builder: (context, index) {
                                return Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    pickerData[columnIndex][index],
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: selecteds[columnIndex] == index
                                          ? Colors.blue
                                          : Colors.black,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 确定按钮
              Padding(
                padding: const EdgeInsets.all(16),
                child: ElevatedButton(
                  onPressed: () {
                    // 构建选中的时间
                    int hour = initialDate.hour;
                    int minute = initialDate.minute;
                    int second = initialDate.second;

                    int currentIndex = 0;

                    if (hasHour) {
                      hour = int.parse(
                          pickerData[currentIndex][selecteds[currentIndex]]);
                      currentIndex++;
                    }

                    if (hasMinute) {
                      minute = int.parse(
                          pickerData[currentIndex][selecteds[currentIndex]]);
                      currentIndex++;
                    }

                    if (hasSecond) {
                      second = int.parse(
                          pickerData[currentIndex][selecteds[currentIndex]]);
                      currentIndex++;
                    }

                    selectedDateTime = DateTime(
                        initialDate.year,
                        initialDate.month,
                        initialDate.day,
                        hour,
                        minute,
                        second);
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    minimumSize: const Size(double.infinity, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    "transaction_confirm".tr,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),

              // 底部安全区域指示器
              Container(
                height: 4,
                width: 40,
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ],
          ),
        );
      },
    );

    if (selectedDateTime != null) {
      return HPDatePickerModel(
        dateStr: HPDateUtil.dateFormat(selectedDateTime!, format: dateFormat),
        dateTime: selectedDateTime!,
      );
    }

    return null;
  }
}
