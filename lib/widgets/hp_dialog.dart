/*
 * ProjectName：UaApp
 * FilePath：lib/widgets
 * FileName：hp_dialog
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/27 18:28:22
 */

import 'package:auapp/theme/theme_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class HPDialog extends Dialog {
  String? title;
  String? content;
  String? confirmText;
  String? cancelText;
  Function? confirmCallback;
  Function? cancelCallback;

  HPDialog({
    super.key,
    this.title,
    this.content,
    this.confirmText,
    this.cancelText,
    this.confirmCallback,
    this.cancelCallback,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Material(
        type: MaterialType.transparency,
        child: Center(
          // 关键：用 Center 控制 Dialog 位置
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: 320.w, // 设置最大宽度
              minWidth: 320.w, // 设置最小宽度
              maxHeight: 500.h, // 设置最大高度
              minHeight: 164.h, // 设置最小高度
            ),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.sp),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (title?.isNotEmpty ?? false) // 条件渲染标题
                    Padding(
                      padding: EdgeInsets.only(top: 32.sp, left: 25.sp, right: 25.sp),
                      child: Text(title!, style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500, color: ThemeConfig.darkTextColor)),
                    ),
                  if (content?.isNotEmpty ?? false) // 条件渲染内容
                    Padding(
                      padding: EdgeInsets.only(top: 8.h, left: 25.w, right: 25.w, bottom: 24.h),
                      child: Text(content ?? "", style: TextStyle(fontSize: 16.sp, color: const Color(0xFF6B7275))),
                    ),
                  Container(
                    height: 0.5.h,
                    color: const Color(0xFFE5E6EB),
                  ),
                  // 添加按钮行
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: 159.w,
                        child: TextButton(
                          onPressed: () => cancelCallback?.call(),
                          child: Text(cancelText ?? "common_btn_cancel".tr, style: TextStyle(color: const Color(0xFF4E5152), fontSize: 16.sp)),
                        ),
                      ),
                      Container(
                        height: 52.h,
                        width: 0.5.w,
                        color: const Color(0xFFE5E6EB),
                      ),
                      SizedBox(
                        width: 159.w,
                        child: TextButton(
                          onPressed: () => confirmCallback?.call(),
                          child: Text(confirmText ?? "common_btn_confirm".tr, style: TextStyle(color: ThemeConfig.mainThemeColor, fontSize: 16.sp)),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  static show(
    BuildContext context, {
    VoidCallback? onConfirmPress,
    VoidCallback? onCancelPress,
    String? title,
    String? content,
    String? confirmText,
    String? cancelText,
  }) {
    showDialog<Null>(
      context: context,
      barrierDismissible: false,
      useRootNavigator: false,
      builder: (cxt) {
        return HPDialog(
          title: title,
          content: content,
          confirmText: confirmText,
          cancelText: cancelText,
          cancelCallback: () {
            Navigator.pop(cxt);
            if (onCancelPress != null) {
              onCancelPress();
            }
          },
          confirmCallback: () {
            Navigator.pop(cxt);
            if (onConfirmPress != null) {
              onConfirmPress();
            }
          },
        );
      },
    );
  }
}
