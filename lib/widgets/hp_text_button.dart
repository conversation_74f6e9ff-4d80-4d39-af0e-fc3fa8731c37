/*
 * @Author: huaze.fan
 * @LastEditTime: 2023-03-13 22:35:34
 * @LastEditors: <EMAIL> <EMAIL>
 * @Description: Good Good Study！Day Day Up
 */
import 'package:flutter/material.dart';

class HPTextButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final ButtonStyle? style;
  final bool noSplash;
  final EdgeInsetsGeometry? padding;
  final Size? minimumSize;

  const HPTextButton({
    Key? key,
    required this.onPressed,
    required this.child,
    this.style,
    this.noSplash = true,
    this.padding,
    this.minimumSize,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ButtonStyle defaultStyle = TextButton.styleFrom(
      padding: padding ?? EdgeInsets.zero,
      minimumSize: minimumSize ?? const Size(1, 1),
      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      splashFactory: noSplash ? NoSplash.splashFactory : null,
    ).merge(style);

    return TextButton(
      onPressed: onPressed,
      style: defaultStyle,
      child: child,
    );
  }
}
