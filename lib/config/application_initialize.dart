/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-16 11:00:31
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-19 09:33:19
 * @FilePath: /hwicc-mobile-flutter/lib/config/application_initialize.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:io';

import 'package:auapp/base/base_binding.dart';
import 'package:auapp/utils/hp_log_util.dart';
import 'package:auapp/vendor/gt3_captcha_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:auapp/services/storage_service.dart';
import 'package:auapp/services/app_info_service.dart';
import '../theme/theme_config.dart';
import 'package:aliyun_push/aliyun_push.dart';
import 'package:flutter/foundation.dart';
import '../pub/constants/push_constants.dart';

/// Class 描述：应用初始化配置
class ApplicationInitialize {
  static Future<void> init() async {
    /// SP 初始化配置
    await Get.putAsync(() => StorageService.init());
    await Get.putAsync(() => AppInfoService().init());

    /// UI 初始化配置
    bool success = await UiSize.init();
    HPLogUtil.v('UiSize 初始化${success ? '成功' : '失败'}！');

    // 导航设置沉浸式
    setCustomSystemUI();

    /// 初始化，获取token
    // FcmUtils().initNotifications();

    /// 初始化极验配置
    Gt3CaptchaUtil.init();
    //初始化推送
    await initAliyunPush();
  }

  static Future<void> initAliyunPush() async {
    try {
      // 初始化阿里云推送
      final push = AliyunPush();
      const config =
          kDebugMode ? PushConstants.debugConfig : PushConstants.releaseConfig;
      await push.initPush(
          appKey: config['appKey'], appSecret: config['appSecret']);

      // 初始化安卓三方推送
      if (Platform.isAndroid) {
        var result = await push.initAndroidThirdPush();
        HPLogUtil.v('阿里云三方推送初始化结果: $result');
      }
      // 监听通知点击事件
      push.addMessageReceiver(
        onMessage: (message) async {
          HPLogUtil.v('收到阿里云推送通知: $message');
        },
        onNotification: (notification) async {
          HPLogUtil.v('收到阿里云推送通知: $notification');
        },
      );
      // 获取设备推送标识
      String? deviceId = await push.getDeviceId();
      HPLogUtil.v('阿里云推送设备ID: $deviceId');
    } catch (e) {
      HPLogUtil.e('阿里云推送初始化失败: $e');
    }
  }

  // 安卓沉浸式设置
  static void setCustomSystemUI() {
    if (Platform.isAndroid) {
      SystemUiOverlayStyle style = const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark);
      SystemChrome.setSystemUIOverlayStyle(style);
    }
  }
}
