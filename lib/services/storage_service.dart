import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService extends GetxService {
  static Future<SharedPreferences> init() async {
    return await SharedPreferences.getInstance();
  }

  static Future<void> setString(String key, String value) async {
    var storage = Get.find<SharedPreferences>();
    await storage.setString(key, value);
  }

  static Future<void> setStringList(String key, List<String> value) async {
    var storage = Get.find<SharedPreferences>();
    await storage.setStringList(key, value);
  }

  static Future<void> setInt(String key, int value) async {
    var storage = Get.find<SharedPreferences>();
    await storage.setInt(key, value);
  }

  static Future<void> setDouble(String key, double value) async {
    var storage = Get.find<SharedPreferences>();
    await storage.setDouble(key, value);
  }

  static Future<void> setBool(String key, bool value) async {
    var storage = Get.find<SharedPreferences>();
    await storage.setBool(key, value);
  }

  static Object? get(String key) {
    var storage = Get.find<SharedPreferences>();
    return storage.get(key);
  }

  static String? getString(String key) {
    var storage = Get.find<SharedPreferences>();
    return storage.getString(key);
  }

  static int? getInt(String key) {
    var storage = Get.find<SharedPreferences>();
    return storage.getInt(key);
  }

  static bool? getBool(String key) {
    var storage = Get.find<SharedPreferences>();
    return storage.getBool(key);
  }

  static List<String>? getStringList(String key) {
    var storage = Get.find<SharedPreferences>();
    return storage.getStringList(key);
  }

  static Future<bool> remove(String key) {
    var storage = Get.find<SharedPreferences>();
    return storage.remove(key);
  }
}
