import 'package:get/get.dart';
import 'dart:io';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:auapp/utils/hp_device_info.dart';
import 'package:auapp/pages/login/models/login_request.dart';

class AppInfoService extends GetxService {
  late String appPlatform;
  late String appVersion;
  late String deviceId;

  Future<AppInfoService> init() async {
    appPlatform = Platform.isAndroid ? "Android" : "iOS";
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    appVersion = packageInfo.version;
    final deviceInfo = await HPDeviceInfo.readDeviceInfo();
    deviceId = deviceInfo['id'] ?? "unknown_device"; // 或者其他唯一标识符
    print(
        'AppInfoService initialized: Platform=$appPlatform, Version=$appVersion, DeviceID=$deviceId');
    return this;
  }

  AppInfo getAppInfo() {
    // 确保在使用前已初始化
    if (!Get.isRegistered<AppInfoService>() ||
        !Get.find<AppInfoService>().initialized) {
      // Fallback or error, though `putAsync` should ensure initialization
      print("Warning: AppInfoService accessed before proper initialization.");
      return AppInfo(
          app_platform: "Unknown",
          app_version: "1.0.0",
          device_id: "fallback_device_id");
    }
    return AppInfo(
      app_platform: appPlatform,
      app_version: appVersion,
      device_id: deviceId,
    );
  }

  Map<String, dynamic> getAppInfoJson() {
    // 确保在使用前已初始化
    if (!Get.isRegistered<AppInfoService>() ||
        !Get.find<AppInfoService>().initialized) {
      print("Warning: AppInfoService accessed before proper initialization.");
      return {
        'app_platform': "Unknown",
        'app_version': "1.0.0",
        'device_id': "fallback_device_id",
      };
    }
    return {
      'app_platform': appPlatform,
      'app_version': appVersion,
      'device_id': deviceId,
    };
  }

  // Helper to check if service is initialized, GetX doesn't directly expose this from `putAsync` readiness easily
  bool get initialized => _initialized;
  bool _initialized = false;

  @override
  void onInit() {
    super.onInit();
    // `init()` is called via Get.putAsync, so this onInit might be redundant
    // or could be used for other synchronous setup if needed.
    // For now, we mark initialized after `init` completes.
  }

  // Override onReady or use a flag set in init
  @override
  void onReady() {
    super.onReady();
    _initialized = true; // Mark as initialized once GetX considers it ready
    print("AppInfoService is ready!");
  }
}
