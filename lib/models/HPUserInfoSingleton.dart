/*
 * ProjectName：UaApp
 * FilePath：lib/models
 * FileName：HPUserInfoSingleton
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/28 15:12:11
 */

import 'dart:convert';

import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/pages/main/models/main_ext_user_detail_info.dart';
import 'package:auapp/pages/merchantSelect/models/sub_user_info.dart';
import 'package:auapp/pub/constants/storage_constants.dart';
import 'package:auapp/services/storage_service.dart';

class HPUserInfoSingleton {
  static final HPUserInfoSingleton _instance = HPUserInfoSingleton._internal();
  factory HPUserInfoSingleton() => _instance;
  HPUserInfoSingleton._internal();

  /// 用户ID
  String? userId;

  /// 子用户编号
  String? subUserId;

  /// 子用户状态（N-正常(Normal) F-冻结(Frozen) C-注销(Cancel)）
  String? status;

  /// 是否需要首次修改密码(Y-是 N-否)
  String? isEditPwd;

  /// 组织编号
  String? orgId;

  /// 客户号
  String? custId;

  /// 客户注册名称
  String? custRegName;

  /// 客户英文名称
  String? custEnName;

  /// 客户简称
  String? custShortName;

  /// 客户状态(N-正常(Normal) C-关闭(Closed))
  String? custStatus;

  /// 子用户类型(1-管理员 2-操作员)
  String? subUserType;

  /// 用户名称
  String? name;

  /// 邮箱
  String? email;

  /// 区号
  String? phoneAreaCode;

  /// 手机号
  String? phone;

  /// 用户状态(枚举：Y-启用 N-禁用)
  String? isActive;

  /// 是否管理员(枚举：Y-是 N-否)
  String? isAdmin;

  /// 退款权限(枚举：Y-开启 N-关闭)
  String? refundPermission;

  ///
  String? memberCode;

  ///
  String? clientId;

  String? id;
  String? loginName;
  String? userType;
  String? phoneMask;
  String? entOrgAdmin;
  String? isOrgAdmin;
  String? huifuId;
  String? roleIds;

  // 初始化时加载本地数据
  Future<void> init() async {
    // 从本地存储中加载用户信息
    String userInfoJson =
        StorageService.getString(StorageConstants.userInfo) ?? "";
    if (userInfoJson.isNotEmpty) {
      SubUserInfo subUserInfo = SubUserInfo.fromJson(json.decode(userInfoJson));
      HPUserInfoSingleton().subUserId = subUserInfo.subUserId;
      HPUserInfoSingleton().status = subUserInfo.status;
      HPUserInfoSingleton().isEditPwd = subUserInfo.isEditPwd;
      HPUserInfoSingleton().orgId = subUserInfo.orgId;
      HPUserInfoSingleton().custId = subUserInfo.custId;
      HPUserInfoSingleton().custRegName = subUserInfo.custRegName;
      HPUserInfoSingleton().custEnName = subUserInfo.custEnName;
      HPUserInfoSingleton().custShortName = subUserInfo.custShortName;
      HPUserInfoSingleton().custStatus = subUserInfo.custStatus;
    }

    String loginOperatorExtInfoJson =
        StorageService.getString(StorageConstants.loginOperatorExtInfo) ?? "";
    if (loginOperatorExtInfoJson.isNotEmpty) {
      MainExtUserDetailInfo mainExtUserDetailInfo =
          MainExtUserDetailInfo.fromJson(json.decode(loginOperatorExtInfoJson));
      HPUserInfoSingleton().subUserId = mainExtUserDetailInfo.sub_user_id;
      HPUserInfoSingleton().subUserType = mainExtUserDetailInfo.sub_user_type;
      HPUserInfoSingleton().userId = mainExtUserDetailInfo.user_id;
      HPUserInfoSingleton().name = mainExtUserDetailInfo.name;
      HPUserInfoSingleton().email = mainExtUserDetailInfo.email;
      HPUserInfoSingleton().phoneAreaCode =
          mainExtUserDetailInfo.phone_area_code;
      HPUserInfoSingleton().phone = mainExtUserDetailInfo.phone;
      HPUserInfoSingleton().isActive = mainExtUserDetailInfo.is_active;
      HPUserInfoSingleton().isAdmin = mainExtUserDetailInfo.is_admin;
      HPUserInfoSingleton().refundPermission =
          mainExtUserDetailInfo.refund_permission;
      HPUserInfoSingleton().id = mainExtUserDetailInfo.id;
      HPUserInfoSingleton().loginName = mainExtUserDetailInfo.login_name;
      HPUserInfoSingleton().userType = mainExtUserDetailInfo.user_type;
      HPUserInfoSingleton().phoneMask = mainExtUserDetailInfo.phone_mask;
      HPUserInfoSingleton().entOrgAdmin = mainExtUserDetailInfo.ent_org_admin;
      HPUserInfoSingleton().isOrgAdmin = mainExtUserDetailInfo.is_org_admin;
      HPUserInfoSingleton().huifuId = mainExtUserDetailInfo.huifu_id;
      HPUserInfoSingleton().roleIds = mainExtUserDetailInfo.role_ids;
      String memberCode = mainExtUserDetailInfo.huifu_id?.split('_')[0] ?? '';
      String clientId = mainExtUserDetailInfo.huifu_id?.split('_')[1] ?? '';
      HPUserInfoSingleton().memberCode = memberCode;
      HPUserInfoSingleton().clientId = clientId;
    }
  }

  // 更新用户信息并保存
  Future<void> updateUserInfo({required SubUserInfo subUserInfo}) async {
    // 保存userinfo
    StorageService.setString(
        StorageConstants.userInfo, json.encode(subUserInfo.toJson()));

    HPUserInfoSingleton().subUserId = subUserInfo.subUserId;
    HPUserInfoSingleton().status = subUserInfo.status;
    HPUserInfoSingleton().isEditPwd = subUserInfo.isEditPwd;
    HPUserInfoSingleton().orgId = subUserInfo.orgId;
    HPUserInfoSingleton().custId = subUserInfo.custId;
    HPUserInfoSingleton().custRegName = subUserInfo.custRegName;
    HPUserInfoSingleton().custEnName = subUserInfo.custEnName;
    HPUserInfoSingleton().custShortName = subUserInfo.custShortName;
    HPUserInfoSingleton().custStatus = subUserInfo.custStatus;

    /// 获取操作员扩展信息
    HPResponse response = await HttpRequest.post(
      ApiConstants.appExtUserDetailInfoUrl,
      params: {
        "ext_sub_user_id": subUserInfo.subUserId,
      },
      showLoading: false,
    );
    if (response.isSuccess) {
      StorageService.setString(
          StorageConstants.loginOperatorExtInfo, json.encode(response.data));
      MainExtUserDetailInfo mainExtUserDetailInfo =
          MainExtUserDetailInfo.fromJson(response.data);
      HPUserInfoSingleton().subUserId = mainExtUserDetailInfo.sub_user_id;
      HPUserInfoSingleton().subUserType = mainExtUserDetailInfo.sub_user_type;
      HPUserInfoSingleton().userId = mainExtUserDetailInfo.user_id;
      HPUserInfoSingleton().name = mainExtUserDetailInfo.name;
      HPUserInfoSingleton().email = mainExtUserDetailInfo.email;
      HPUserInfoSingleton().phoneAreaCode =
          mainExtUserDetailInfo.phone_area_code;
      HPUserInfoSingleton().phone = mainExtUserDetailInfo.phone;
      HPUserInfoSingleton().isActive = mainExtUserDetailInfo.is_active;
      HPUserInfoSingleton().isAdmin = mainExtUserDetailInfo.is_admin;
      HPUserInfoSingleton().refundPermission =
          mainExtUserDetailInfo.refund_permission;
      HPUserInfoSingleton().id = mainExtUserDetailInfo.id;
      HPUserInfoSingleton().loginName = mainExtUserDetailInfo.login_name;
      HPUserInfoSingleton().userType = mainExtUserDetailInfo.user_type;
      HPUserInfoSingleton().phoneMask = mainExtUserDetailInfo.phone_mask;
      HPUserInfoSingleton().entOrgAdmin = mainExtUserDetailInfo.ent_org_admin;
      HPUserInfoSingleton().isOrgAdmin = mainExtUserDetailInfo.is_org_admin;
      HPUserInfoSingleton().huifuId = mainExtUserDetailInfo.huifu_id;
      HPUserInfoSingleton().roleIds = mainExtUserDetailInfo.role_ids;
      String memberCode = mainExtUserDetailInfo.huifu_id?.split('_')[0] ?? '';
      String clientId = mainExtUserDetailInfo.huifu_id?.split('_')[1] ?? '';
      HPUserInfoSingleton().memberCode = memberCode;
      HPUserInfoSingleton().clientId = clientId;
    }
  }

  // 清除用户信息
  Future<void> clearUserInfo() async {
    name = null;
    email = null;
    userId = null;
    subUserId = null;
    status = null;
    isEditPwd = null;
    orgId = null;
    custId = null;
    custRegName = null;
    custEnName = null;
    custShortName = null;
    custStatus = null;
    subUserType = null;
    phoneAreaCode = null;
    phone = null;
    isActive = null;
    isAdmin = null;
    refundPermission = null;
    memberCode = null;
    clientId = null;
    id = null;
    loginName = null;
    userType = null;
    phoneMask = null;
    entOrgAdmin = null;
    isOrgAdmin = null;
    huifuId = null;
    roleIds = null;
    StorageService.remove(StorageConstants.loginOperatorExtInfo);
    StorageService.remove(StorageConstants.userInfo);
  }
}
