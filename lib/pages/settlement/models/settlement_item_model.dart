class SettlementItemModel {
  num? settleBatchId; //结算批次号
  String? remitCreateTime; //结算时间，英文格式【DD-MM hh-mm-ss】，中文格式【MM-DD hh-mm-ss】
  num? finalSettleAmount; //结算金额
  String? finalSettleCur; //结算币种

  SettlementItemModel({
    this.settleBatchId,
    this.remitCreateTime,
    this.finalSettleAmount,
    this.finalSettleCur,
  });

  SettlementItemModel.fromJson(Map<String, dynamic> json) {
    settleBatchId = json['settleBatchId'];
    remitCreateTime = json['remitCreateTime'];
    finalSettleAmount = json['finalSettleAmount'];
    finalSettleCur = json['finalSettleCur'];
  }
}
