import 'package:auapp/utils/hp_amount_util.dart';

class SettlementSummaryModel {
  num? preSettlementAmount; //待结算金额
  num? orderSumAmount; //订单金额
  num? merchantTrxFee; //商户交易手续费;
  num? clientTrxFee; //客户手续费
  SettlementSummaryModel({
    this.preSettlementAmount,
    this.orderSumAmount,
    this.merchantTrxFee,
    this.clientTrxFee,
  });

  SettlementSummaryModel.fromJson(Map<String, dynamic> json) {
    preSettlementAmount = json['preSettlementAmount'];
    orderSumAmount = json['orderSumAmount'];
    merchantTrxFee = json['merchantTrxFee'];
    clientTrxFee = json['clientTrxFee'];
  }

  String get formattedPreSettlementAmount {
    return HPAmountUtil.formatWithCommaDeftVal(preSettlementAmount.toString(),
        defaultVal: '0.00');
  }

  String get formattedOrderSumAmount {
    return HPAmountUtil.formatWithCommaDeftVal(orderSumAmount.toString(),
        defaultVal: '0.00');
  }

  String get formattedMerchantTrxFee {
    return HPAmountUtil.formatWithCommaDeftVal(merchantTrxFee.toString(),
        defaultVal: '0.00');
  }

  String get formattedClientTrxFee {
    return HPAmountUtil.formatWithCommaDeftVal(clientTrxFee.toString(),
        defaultVal: '0.00');
  }
}
