import 'package:auapp/utils/hp_amount_util.dart';
import 'package:flutter/material.dart';
import '../models/settlement_item_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SettlementListItemWidget extends StatelessWidget {
  final SettlementItemModel item;

  const SettlementListItemWidget({Key? key, required this.item})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 根据设计稿，字体大小和间距单位为 sp，在 Flutter 中直接使用逻辑像素值
    // 如果项目中有特定的屏幕适配方案，请自行调整
    return Container(
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'NO.${item.settleBatchId ?? ''}',
                  style:
                      TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 4.0),
                Text(
                  item.remitCreateTime ?? '',
                  style: TextStyle(
                      fontSize: 13.sp, color: const Color(0xff6B7275)),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$ ${HPAmountUtil.formatWithCommaDeftVal(item.finalSettleAmount.toString(), defaultVal: '')}',
                  style: TextStyle(
                      fontFamily: "PingFangSC",
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 4.0),
                Text(
                  item.finalSettleCur ?? '',
                  style: const TextStyle(fontSize: 12.0, color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
