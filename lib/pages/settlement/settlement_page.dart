import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../widgets/hp_app_bar.dart'; // 使用相对路径导入 AppBar
import '../../r.dart'; // 导入资源文件
import './settlement_controller.dart';
import './widgets/settlement_list_item_widget.dart';
import './models/settlement_summary_model.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SettlementPage extends GetView<SettlementController> {
  const SettlementPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
    );
    // 根据设计稿，字体大小和间距单位为 sp，在 Flutter 中直接使用逻辑像素值
    // 如果项目中有特定的屏幕适配方案，请自行调整
    final ScrollController scrollController = ScrollController();

    // 监听滚动事件以实现上拉加载
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
              scrollController.position.maxScrollExtent - 200 && // 预加载阈值
          !controller.isLoadingMore.value &&
          controller.hasMore.value) {
        controller.loadMoreSettlementItems();
      }
    });

    return Scaffold(
      backgroundColor: const Color(0xffF4F6FB),
      extendBodyBehindAppBar: true, // 实现沉浸式标题栏
      appBar: HPAppBar(
        text: "settlement_title".tr,
        backgroundColor: Colors.transparent, // 透明背景
        elevation: 0,
      ),
      body: Stack(
        children: [
          // 背景图片层
          Positioned(
            left: 0,
            top: 0,
            right: 0,
            height: 278.h,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16.r),
                  bottomRight: Radius.circular(16.r),
                ),
              ),
              child: Image.asset(
                height: 192.h, // 背景图片高度
                R.assetsImagesTransactionBackgroundImg,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            child: Obx(() {
              if (controller.summaryData.value == null &&
                  controller.settlementItems.isEmpty &&
                  !controller.isLoadingMore.value) {
                // 初始加载状态

                return const Center(child: CircularProgressIndicator());
              }
              return CustomScrollView(
                controller: scrollController,
                slivers: [
                  SliverToBoxAdapter(
                    child: _buildSummarySection(controller.summaryData.value),
                  ),
                  SliverToBoxAdapter(
                    child: Container(
                      margin: const EdgeInsets.only(top: 8),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16.r),
                          topRight: Radius.circular(16.r),
                        ),
                      ),
                      padding: EdgeInsets.symmetric(
                          horizontal: 16.w, vertical: 15.h),
                      child: Text(
                        "settlements_all".tr,
                        style: TextStyle(
                            fontSize: 16.sp, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        if (index < controller.settlementItems.length) {
                          final item = controller.settlementItems[index];
                          return Column(
                            children: [
                              SettlementListItemWidget(item: item),
                              if (index <
                                  controller.settlementItems.length -
                                      1) // 最后一条数据不下划线
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 16.w),
                                  child: Divider(
                                      height: 1.h,
                                      color: const Color(0xFFF0F0F0)),
                                )
                            ],
                          );
                        }
                        return null; // 不会到达这里，因为 childCount 做了限制
                      },
                      childCount: controller.settlementItems.length,
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Obx(() {
                      if (controller.isLoadingMore.value) {
                        return Padding(
                          padding: EdgeInsets.all(16.w),
                          child:
                              const Center(child: CircularProgressIndicator()),
                        );
                      } else if (!controller.hasMore.value &&
                          controller.settlementItems.isNotEmpty) {
                        return Padding(
                          padding: EdgeInsets.all(16.w),
                          child: Center(
                              child: Text("this_is_the_bottom".tr,
                                  style: const TextStyle(color: Colors.grey))),
                        );
                      } else if (controller.hasMore.value &&
                          controller.settlementItems.isNotEmpty) {
                        return Padding(
                          padding: EdgeInsets.all(16.w),
                          child: Center(
                              child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text("pull_up_to_load_more".tr,
                                  style: TextStyle(
                                      color: Colors.grey, fontSize: 12.spMin)),
                              SizedBox(width: 4.w),
                              Icon(Icons.arrow_upward,
                                  size: 14.w, color: Colors.grey),
                            ],
                          )),
                        );
                      }
                      return const SizedBox.shrink(); // 如果列表为空，不显示任何提示
                    }),
                  ),
                ],
              );
            }),
          ),
          // 内容层
        ],
      ),
    );
  }

  Widget _buildSummarySection(SettlementSummaryModel? summary) {
    // if (summary == null) {
    //   return Container(
    //     height: 320,
    //     padding: EdgeInsets.only(
    //         top:
    //             MediaQuery.of(Get.context!).padding.top + 60), // 考虑状态栏和AppBar高度
    //     child: const Center(child: CircularProgressIndicator()),
    //   );
    // }
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(Get.context!).padding.top + 56.h, // 状态栏 + AppBar 高度
        left: 16.w,
        right: 16.w,
      ),
      height: 278.h, // 与背景图片高度一致
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            summary?.formattedPreSettlementAmount ?? '',
            style: TextStyle(
              fontSize: 28.sp,
              color: ThemeConfig.norBackColor,
              fontFamily: "DingTalk-JinBuTi",
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            "${"settlement_pre_amount_title".tr}(AUD)",
            style: TextStyle(
              fontSize: 11.sp,
              color: ThemeConfig.norGrayColor, // 半透明白色
            ),
          ),
          SizedBox(height: 20.h),
          Container(
            constraints: BoxConstraints(
              minHeight: 82.h,
            ),
            padding: EdgeInsets.symmetric(vertical: 16.w, horizontal: 12.h),
            decoration: BoxDecoration(
                color: const Color(0xffF1F9FE), // 提高透明度以更好显示
                borderRadius: BorderRadius.circular(8.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 2), // changes position of shadow
                  ),
                ]),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSummaryItem('=', summary?.formattedOrderSumAmount ?? '',
                    "settlement_order_amount".tr),
                _buildSummaryItem('-', summary?.formattedMerchantTrxFee ?? '',
                    "settlement_transaction_fee".tr),
                _buildSummaryItem('+', summary?.formattedClientTrxFee ?? '',
                    "settlement_customer_bears_the_service_fee".tr),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String operator, String amount, String label) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(operator,
            style: TextStyle(
                fontSize: 14.sp,
                color: ThemeConfig.norGrayColor,
                fontFamily: "DingTalk-JinBuTi")),
        SizedBox(width: 8.w),
        Container(
          constraints: BoxConstraints(
            maxWidth: 92.w,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(amount,
                  style: TextStyle(
                      color: ThemeConfig.norBackColor,
                      fontSize: 14.sp,
                      fontFamily: "DingTalk-JinBuTi")),
              Text(
                label,
                textAlign: TextAlign.center,
                style:
                    TextStyle(fontSize: 11.sp, color: ThemeConfig.norGrayColor),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
