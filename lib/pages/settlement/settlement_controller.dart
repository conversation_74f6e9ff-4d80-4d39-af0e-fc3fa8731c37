import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/models/HPUserInfoSingleton.dart';
import 'package:auapp/pages/settlement/models/settlement_list.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:auapp/utils/hp_log_util.dart';
import 'package:get/get.dart';
import './models/settlement_item_model.dart';
import './models/settlement_summary_model.dart';

class SettlementController extends GetxController {
  // 模拟的摘要数据
  final summaryData = Rx<SettlementSummaryModel?>(null);

  // 模拟的结算列表数据
  final settlementItems = <SettlementItemModel>[].obs;

  // 是否正在加载更多
  final isLoadingMore = false.obs;

  // 是否还有更多数据
  final hasMore = true.obs;

  // 当前页码，用于分页加载
  int _currentPage = 1;
  final int _itemsPerPage = 10; //每页加载10条

  @override
  void onInit() {
    super.onInit();
    fetchSettlementSummary();
    fetchInitialSettlementItems();
  }

  // 获取结算摘要信息
  void fetchSettlementSummary() async {
    try {
      String memberCode = HPUserInfoSingleton().memberCode ?? '';
      String clientId = HPUserInfoSingleton().clientId ?? '';
      HPResponse response = await HttpRequest.post(
          ApiConstants.settleSummaryUrl,
          customBaseUrl: ApiConstants.baseTransactionUrl,
          params: {'memberCode': memberCode, "clientId": clientId});
      if (response.isSuccess) {
        final res = SettlementSummaryModel.fromJson(response.data);
        summaryData.value = res;
      } else {
        HPLoading.showToast(response.msg);
      }
    } catch (e) {
      HPLogUtil.e(e.toString());
      HPLoading.showToast(e.toString());
    }
  }

  // 获取初始结算列表项
  void fetchInitialSettlementItems() async {
    _currentPage = 1;
    hasMore.value = true;
    settlementItems.clear();
    await _fetchSettlementItems();
  }

  // 加载更多结算列表项
  void loadMoreSettlementItems() async {
    if (isLoadingMore.value || !hasMore.value) return;

    isLoadingMore.value = true;
    _currentPage++;
    await _fetchSettlementItems();
    isLoadingMore.value = false;
  }

  // 内部方法：获取结算列表项（分页）
  Future<void> _fetchSettlementItems() async {
    try {
      // 此处生成20条mock数据
      // for (int i = 0; i < 20; i++) {
      //   settlementItems.add(SettlementItemModel(
      //     settleBatchId: '1234567890${i + 1}',
      //     remitCreateTime: DateTime.now().toString(),
      //     finalSettleAmount: '100.00',
      //     finalSettleCur: 'AUD',
      //   ));
      // }
      String memberCode = HPUserInfoSingleton().memberCode ?? '';
      String clientId = HPUserInfoSingleton().clientId ?? '';
      HPResponse response = await HttpRequest.post(
          ApiConstants.settleBatchListUrl,
          customBaseUrl: ApiConstants.baseTransactionUrl,
          params: {
            'memberCode': memberCode,
            "clientId": clientId,
            "pageNum": _currentPage.toString(),
            "pageSize": _itemsPerPage.toString()
          });
      if (response.isSuccess) {
        final res = SettlementListData.fromJson(response.data);
        final total = response.total ?? 0;
        if (res.settlementList != null) {
          settlementItems.addAll(res.settlementList!);
        }
        hasMore.value = settlementItems.length < total;
      } else {
        HPLoading.showToast(response.msg);
      }
    } catch (e) {
      HPLogUtil.e(e.toString());
      HPLoading.showToast(e.toString());
    }
  }
}
