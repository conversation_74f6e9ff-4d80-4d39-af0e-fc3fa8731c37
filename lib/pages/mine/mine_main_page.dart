import 'package:auapp/models/HPUserInfoSingleton.dart';
import 'package:auapp/pages/home/<USER>/switch_merchant_sheet.dart';
import 'package:auapp/pages/main/main_controller.dart';
import 'package:auapp/pages/mine/mine_main_controller.dart';
import 'package:auapp/r.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/widgets/hp_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class MineMainPage extends GetView<MineMainController> {
  MineMainPage({super.key});
  final MainController mainController = Get.find<MainController>();

  final List<Map<String, String>> funcList = [
    // {"name": "mine_merchant_info".tr, "img": R.assetsImagesMineMerinfoIcon},
    // {"name": "mine_bank_card".tr, "img": R.assetsImagesMineBankIcon},
    // {"name": "mine_settlements".tr, "img": R.assetsImagesMineSettleIcon},
    // {"name": "mine_invoices".tr, "img": R.assetsImagesMineReceiptIcon},
    // {"name": "mine_helps".tr, "img": R.assetsImagesMineHelpIcon},
    {
      "name": "mine_settings".tr,
      "img": R.assetsImagesMineSetIcon,
      "route": Routes.setting
    },
  ];

  /**
   * 顶部商户名称切换
   */
  Widget _merNameWidget(BuildContext context) {
    return Obx(() => HPTextButton(
          onPressed: () {
            SwitchMerchantSheet.show();
          },
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Row(
              children: [
                Container(
                  constraints: BoxConstraints(maxWidth: 1.sw - 80.w),
                  child: Text(
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    mainController.getCurrentMerchantName(),
                    style: ThemeConfig.boldBackText18,
                  ),
                ),
                Image.asset(
                  R.assetsImagesHomeMerDropdown,
                  width: 24.w,
                  height: 24.w,
                ),
              ],
            ),
          ),
        ));
  }

  List<Widget> _topBackgroundWidget(BuildContext context) {
    return [
      Positioned(
          top: 0,
          left: 0,
          right: 0,
          height: 324.h,
          child: Container(
            decoration: BoxDecoration(
                image: DecorationImage(
              image: AssetImage(R.assetsImagesHomeBackgroundImg),
              fit: BoxFit.cover,
            )),
          )),
      Positioned(
        top: 112.5.h,
        right: 0,
        width: 146.w,
        height: 135.h,
        child: Container(
          decoration: BoxDecoration(
              image: DecorationImage(
            image: AssetImage(R.assetsImagesMineBackgroundIcon),
            fit: BoxFit.cover,
          )),
        ),
      ),
      Positioned(
        top: MediaQuery.of(context).padding.top + 8.h,
        left: 0,
        right: 0,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [_merNameWidget(context)],
        ),
      ),
      Positioned(
        top: 112.h,
        left: 24.w,
        right: 24.w,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Obx(() => Text(
                  controller.summaryModel.value.formattedPreSettlementAmount,
                  style: ThemeConfig.boldBackAmount18.copyWith(fontSize: 28.sp),
                )),
            SizedBox(
              height: 4.h,
            ),
            Text(
              "${'mine_pre_settlement_amount'.tr}(AUD)",
              style: ThemeConfig.norGrayText12.copyWith(fontSize: 11.sp),
            ),
          ],
        ),
      ),
      Positioned(
        top: 198.h,
        left: 16.w,
        right: 16.w,
        child: Obx(() => _returnAmountWidget()),
      )
    ];
  }

  /**
   * 显示金额计算公式
   */
  Widget _returnAmountWidget() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 8.5.w),
      constraints: BoxConstraints(minHeight: 82.h),
      decoration: BoxDecoration(
        color: Color(0xCCFFFFFF),
        borderRadius: BorderRadius.circular(14),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.5.h),
            child: Text("=",
                style: ThemeConfig.boldBackAmount18.copyWith(
                    fontSize: 14.sp, color: ThemeConfig.norGrayColor)),
          ),
          Column(
            children: [
              Text(controller.summaryModel.value.formattedOrderSumAmount,
                  style: ThemeConfig.boldBackAmount18),
              Text("mine_order_amount".tr,
                  style: ThemeConfig.norGrayText12.copyWith(fontSize: 11.sp)),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(top: 20.5.h),
            child: Text("+",
                style: ThemeConfig.boldBackAmount18.copyWith(
                    fontSize: 14.sp, color: ThemeConfig.norGrayColor)),
          ),
          Column(
            children: [
              Text(controller.summaryModel.value.formattedMerchantTrxFee,
                  style: ThemeConfig.boldBackAmount18),
              Text("mine_transaction_fee".tr,
                  style: ThemeConfig.norGrayText12.copyWith(fontSize: 11.sp)),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(top: 20.5.h),
            child: Text("-",
                style: ThemeConfig.boldBackAmount18.copyWith(
                    fontSize: 14.sp, color: ThemeConfig.norGrayColor)),
          ),
          Container(
            constraints: BoxConstraints(
              maxWidth: 92.w,
            ),
            child: Column(
              children: [
                Text(controller.summaryModel.value.formattedClientTrxFee,
                    style: ThemeConfig.boldBackAmount18),
                Text("mine_customer_bear_fee".tr,
                    style: ThemeConfig.norGrayText12.copyWith(fontSize: 11.sp)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _returnCell(String title, String img, String? route) {
    return HPTextButton(
      onPressed: () {
        if (route != null) {
          HPRouter.pushPage(route);
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 24.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Image.asset(
                  img,
                  width: 24.w,
                  height: 24.w,
                ),
                SizedBox(width: 16.w),
                Text(
                  title,
                  style: ThemeConfig.boldBackText18.copyWith(fontSize: 15.sp),
                ),
              ],
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16.0,
              color: ThemeConfig.norBackColor,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
    );
    return Scaffold(
      appBar: null,
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          ..._topBackgroundWidget(context),
          Positioned(
            top: 307.h,
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.sp),
                  topRight: Radius.circular(16.sp),
                ),
              ),
              padding: EdgeInsets.only(top: 20.h),
              child: ListView(
                padding: const EdgeInsets.all(0),
                shrinkWrap: false,
                children: [
                  ...funcList.map(
                      (e) => _returnCell(e["name"]!, e["img"]!, e["route"])),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
