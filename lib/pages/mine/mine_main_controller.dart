import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/base/base_controller.dart';
import 'package:auapp/models/HPUserInfoSingleton.dart';
import 'package:auapp/pages/settlement/models/settlement_summary_model.dart';
import 'package:auapp/pub/constants/storage_constants.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/services/storage_service.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:get/get.dart';

class MineMainController extends BaseController {
  MineMainController();

  final summaryModel = SettlementSummaryModel().obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();

    fetchSettlementData();
  }

  Future<void> logout() async {
    try {
      HPResponse response = await HttpRequest.get(ApiConstants.logoutUrl);
      if (response.isSuccess) {
        StorageService.setBool(StorageConstants.loginFlag, false);
        HPRouter.resolvePage(Routes.login);
      } else {
        HPLoading.showError(response.msg);
      }
    } catch (e) {
      HPLoading.showToast(e.toString());
    }
  }

  // 获取结算数据
  void fetchSettlementData() async {
    try {
      String memberCode = HPUserInfoSingleton().memberCode ?? '';
      String clientId = HPUserInfoSingleton().clientId ?? '';
      HPResponse response = await HttpRequest.post(
          ApiConstants.settleSummaryUrl,
          showLoading: false,
          customBaseUrl: ApiConstants.baseTransactionUrl,
          params: {'memberCode': memberCode, "clientId": clientId});
      if (response.isSuccess) {
        final res = response.data;
        if (res != null) {
          summaryModel.value = SettlementSummaryModel.fromJson(res);
        }
      } else {
        HPLoading.showToast(response.msg);
      }
    } catch (e) {
      HPLoading.showToast(e.toString());
    }
  }
}
