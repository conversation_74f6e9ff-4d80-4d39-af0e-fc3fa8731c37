/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-19 15:24:15
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-23 17:05:32
 * @FilePath: /hwicc-mobile-flutter/lib/pages/home/<USER>/home_data_widget.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:auapp/pages/home/<USER>';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/utils/hp_amount_util.dart';
import 'package:auapp/utils/hp_date_util.dart';
import 'package:auapp/widgets/hp_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class HomeDataWidget extends StatelessWidget {
  HomeDataWidget({
    super.key,
  });

  final controller = Get.find<HomeMainController>();

  final List<Map<String, String>> dateTypeList = [
    {"type": HomeSelectDateType.todayType.name, "name": "home_today".tr},
    {"type": HomeSelectDateType.weekType.name, "name": "home_this_week".tr},
    {"type": HomeSelectDateType.monthType.name, "name": "home_this_month".tr},
    {"type": HomeSelectDateType.yearType.name, "name": "home_this_year".tr},
  ];

  Widget _dateTypeWidget() {
    return Row(
      children: dateTypeList.map((e) {
        return HPTextButton(
            onPressed: () {
              if (controller.currDateType.value.name == e["type"]) {
                return;
              }
              controller.switchDateType(HomeSelectDateType.values
                  .firstWhere((element) => element.name == e["type"]));
            },
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 9.w),
              child: Column(
                children: [
                  Text(e["name"]!,
                      style: ThemeConfig.boldBackText18.copyWith(
                          fontSize: 12.sp, fontWeight: FontWeight.bold)),
                  // 被选中的下面展示下划线
                  Container(
                    width: 24.w,
                    height: 3.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3.h),
                      color: controller.currDateType.value.name == e["type"]
                          ? const Color(0xFF0FACF5)
                          : Colors.transparent,
                    ),
                  ),
                ],
              ),
            ));
      }).toList(),
    );
  }

  Widget _dataTypeWidget() {
    final List<Map<String, dynamic>> dataStrList = [
      {
        "num": controller.transactionInfoSummaryModel.value.totalSuccessOrder,
        "name": "transaction_number_successful".tr,
      },
      {
        "num": HPAmountUtil.formatAbsComma(
            controller.transactionInfoSummaryModel.value.totalRefundAmount,
            absValue: true),
        "name": "${"transaction_refund_amount".tr}(AUD)",
      },
      {
        "num": controller.transactionInfoSummaryModel.value.totalRefundOrder,
        "name": "transaction_number_refund".tr,
      },
    ];

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: dataStrList.map((e) {
        return Container(
          padding: EdgeInsets.only(right: 16.w),
          constraints: BoxConstraints(
            maxWidth: 110.w,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                "${e["num"]}",
                style: ThemeConfig.boldBackAmount18.copyWith(fontSize: 16.sp),
              ),
              Text(
                e["name"],
                style: ThemeConfig.norGrayText12.copyWith(fontSize: 11.sp),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 8.h, left: 24.w),
              child: Text(
                "${HPDateUtil.stringFormatLocale(controller.beginDate.value)} 00:00 - ${HPDateUtil.stringFormatLocale(controller.endDate.value)} 23:59 UTC+8",
                style: ThemeConfig.norGrayText12,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 15.w, top: 16.w),
              child: _dateTypeWidget(),
            ),
            Padding(
              padding: EdgeInsets.only(top: 32.h, left: 24.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    HPAmountUtil.formatAbsComma(controller
                        .transactionInfoSummaryModel.value.totalAmount),
                    style:
                        ThemeConfig.boldBackAmount18.copyWith(fontSize: 28.sp),
                  ),
                  Text(
                    "${"transaction_successful_amount".tr}(AUD)",
                    style: ThemeConfig.norGrayText12.copyWith(fontSize: 11.sp),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  _dataTypeWidget(),
                ],
              ),
            ),
          ],
        ));
  }
}
