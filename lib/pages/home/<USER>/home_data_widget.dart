/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-19 15:24:15
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-23 17:05:32
 * @FilePath: /hwicc-mobile-flutter/lib/pages/home/<USER>/home_data_widget.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:auapp/pages/home/<USER>';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/utils/hp_amount_util.dart';
import 'package:auapp/utils/hp_date_util.dart';
import 'package:auapp/widgets/hp_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class HomeDataWidget extends StatelessWidget {
  HomeDataWidget({
    super.key,
  });

  final controller = Get.find<HomeMainController>();

  final List<Map<String, String>> dateTypeList = [
    {"type": HomeSelectDateType.todayType.name, "name": "home_today".tr},
    {"type": HomeSelectDateType.weekType.name, "name": "home_this_week".tr},
    {"type": HomeSelectDateType.monthType.name, "name": "home_this_month".tr},
    {"type": HomeSelectDateType.yearType.name, "name": "home_this_year".tr},
  ];

  // Widget _dateTypeWidget() {
  //   return Row(
  //     children: dateTypeList.map((e) {
  //       return HPTextButton(
  //           onPressed: () {
  //             if (controller.currDateType.value.name == e["type"]) {
  //               return;
  //             }
  //             controller.switchDateType(HomeSelectDateType.values
  //                 .firstWhere((element) => element.name == e["type"]));
  //           },
  //           child: Padding(
  //             padding: EdgeInsets.symmetric(horizontal: 9.w),
  //             child: Column(
  //               children: [
  //                 Text(e["name"]!,
  //                     style: ThemeConfig.boldBackText18.copyWith(
  //                         fontSize: 12.sp, fontWeight: FontWeight.bold)),
  //                 // 被选中的下面展示下划线
  //                 Container(
  //                   width: 24.w,
  //                   height: 3.h,
  //                   decoration: BoxDecoration(
  //                     borderRadius: BorderRadius.circular(3.h),
  //                     color: controller.currDateType.value.name == e["type"]
  //                         ? const Color(0xFF0FACF5)
  //                         : Colors.transparent,
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ));
  //     }).toList(),
  //   );
  // }

  Widget _dateTypeWidget() {
    final int itemCount = dateTypeList.length;
    // 获取当前选中项的索引
    final int currentIndex = dateTypeList
        .indexWhere((e) => controller.currDateType.value.name == e["type"]);
    // 处理未找到的情况（理论上不应发生），默认为0
    final int validCurrentIndex = currentIndex == -1 ? 0 : currentIndex;

    return SizedBox(
        width: 200.w,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 保持与原布局对齐一致
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: dateTypeList.asMap().entries.map((entry) {
                Map<String, String> itemData = entry.value;
                bool isSelected =
                    controller.currDateType.value.name == itemData["type"];

                return Expanded(
                    child: HPTextButton(
                  onPressed: () {
                    if (isSelected) {
                      // 如果已经是选中状态，则不执行任何操作
                      return;
                    }
                    controller.switchDateType(HomeSelectDateType.values
                        .firstWhere(
                            (element) => element.name == itemData["type"]));
                  },
                  // child: Padding(
                  // padding: EdgeInsets.symmetric(horizontal: 9.w),
                  child: Column(
                    children: [
                      Text(itemData["name"]!,
                          style: ThemeConfig.boldBackText18.copyWith(
                            fontSize: 12.sp,
                            fontWeight: isSelected
                                ? FontWeight.w500
                                : FontWeight.normal,
                          )),
                    ],
                  ),
                ));
              }).toList(),
            ),
            // 动画下划线
            LayoutBuilder(
              builder: (context, constraints) {
                // final double totalWidth = constraints.maxWidth; // 获取可用总宽度
                final double itemWidth = 200.w / itemCount; // 计算每个项目的宽度
                final double underlineWidth = 22.w; // 下划线的固定宽度
                final double underlineHeight = 3.h; // 下划线的高度

                // 计算下划线的左边距，使其在选中项下方居中
                // validCurrentIndex 是当前选中项的索引
                // itemWidth 是每个日期选项（如“今日”、“本周”）的宽度
                // (itemWidth - underlineWidth) / 2 是为了将下划线在单个 itemWidth 内居中对齐所需的偏移量
                final double leftOffset = validCurrentIndex * itemWidth +
                    (itemWidth - underlineWidth) / 2;
                return SizedBox(
                  // 使用 Container 包裹 Stack，明确下划线区域的大小
                  height: underlineHeight,
                  child: Stack(
                    children: [
                      AnimatedPositioned(
                        duration: const Duration(milliseconds: 250), // 动画时长
                        curve: Curves.easeInOut, // 动画曲线，使动画更平滑
                        left: leftOffset,
                        top: 0, // 对齐到 Container 底部
                        width: underlineWidth,
                        height: underlineHeight,
                        child: Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(3.h),
                              color: const Color(0xFF0FACF5)),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ));
  }

  Widget _dataTypeWidget() {
    final List<Map<String, dynamic>> dataStrList = [
      {
        "num": controller.transactionInfoSummaryModel.value.totalSuccessOrder,
        "name": "transaction_number_successful".tr,
      },
      {
        "num": HPAmountUtil.formatAbsComma(
            controller.transactionInfoSummaryModel.value.totalRefundAmount,
            absValue: true),
        "name": "${"transaction_refund_amount".tr}(AUD)",
      },
      {
        "num": controller.transactionInfoSummaryModel.value.totalRefundOrder,
        "name": "transaction_number_refund".tr,
      },
    ];

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: dataStrList.map((e) {
        return Container(
          padding: EdgeInsets.only(right: 16.w),
          constraints: BoxConstraints(
            maxWidth: 110.w,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                "${e["num"]}",
                style: ThemeConfig.boldBackAmount18.copyWith(fontSize: 16.sp),
              ),
              Text(
                e["name"],
                style: ThemeConfig.norGrayText12.copyWith(fontSize: 11.sp),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 8.h, left: 24.w),
              child: Text(
                "${HPDateUtil.stringFormatLocale(controller.beginDate.value, format: 'yyyy/MM/dd', enFormat: 'dd MM,yyyy')} 00:00 - ${HPDateUtil.stringFormatLocale(controller.endDate.value, format: 'yyyy/MM/dd', enFormat: 'dd MM,yyyy')} 23:59 UTC+8",
                style: ThemeConfig.norGrayText12,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 15.w, top: 16.w),
              child: _dateTypeWidget(),
            ),
            Padding(
              padding: EdgeInsets.only(top: 32.h, left: 24.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    HPAmountUtil.formatAbsComma(controller
                        .transactionInfoSummaryModel.value.totalAmount),
                    style:
                        ThemeConfig.boldBackAmount18.copyWith(fontSize: 28.sp),
                  ),
                  Text(
                    "${"transaction_successful_amount".tr}(AUD)",
                    style: ThemeConfig.norGrayText12.copyWith(fontSize: 11.sp),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  _dataTypeWidget(),
                ],
              ),
            ),
          ],
        ));
  }
}
