import 'package:auapp/pages/home/<USER>';
import 'package:auapp/pages/main/main_controller.dart';
import 'package:auapp/pages/merchantSelect/merchant_select_controller.dart';
import 'package:auapp/pages/merchantSelect/models/sub_user_info.dart';
import 'package:auapp/pub/constants/color_constants.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:auapp/widgets/hp_border_button.dart';
import 'package:auapp/widgets/hp_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SwitchMerchantSheet {
  /// 显示商户切换底部弹出框
  static Future<void> show() async {
    try {
      final MainController mainController = Get.find<MainController>();

      // 如果只有一个商户，不显示切换界面
      if (!mainController.hasMultipleMerchants) {
        return;
      }

      await Get.bottomSheet(
        _SwitchMerchantSheetWidget(
          subSelectList: mainController.merchantList,
          onConfirm: (_) {
            Get.delete<MerchantSelectController>(tag: 'merchant_sheet');
            Get.back();
          },
        ),
        backgroundColor: Colors.white,
        isScrollControlled: true,
        clipBehavior: Clip.antiAliasWithSaveLayer,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.w),
            topRight: Radius.circular(16.w),
          ),
        ),
      );
    } catch (e) {
      HPLoading.showToast(e.toString());
    }
  }
}

class _SwitchMerchantSheetWidget extends StatelessWidget {
  final Function(SubUserInfo) onConfirm;
  final List<SubUserInfo> subSelectList;
  _SwitchMerchantSheetWidget({
    required this.subSelectList,
    required this.onConfirm,
    super.key,
  });

  final HomeMainController homeMainController = Get.find<HomeMainController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.7,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildHeader(context),
          _buildMerchantList(),
          _buildConfirmButton(context),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(top: 14.h, bottom: 14.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 24.sp,
          ),
          Expanded(
              child: Text(
            "choose_title".tr,
            textAlign: TextAlign.center,
            style: ThemeConfig.boldBackText18.copyWith(fontSize: 16.sp),
          )),
          HPTextButton(
            onPressed: () => Get.back(),
            padding: EdgeInsets.all(0.w),
            child: Padding(
              padding: EdgeInsets.only(right: 16.sp, left: 16.sp),
              child: Icon(
                Icons.close,
                size: 24.sp,
                color: ColorConstants.darkTextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建商户列表
  Widget _buildMerchantList() {
    return Flexible(
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
        ),
        child: ListView.builder(
          padding: EdgeInsets.only(top: 16.h),
          itemCount: subSelectList.length,
          itemBuilder: (context, index) {
            final merchant = subSelectList[index];
            return Obx(() => _buildMerchantItem(merchant, index));
          },
        ),
      ),
    );
  }

  /// 构建商户项
  Widget _buildMerchantItem(SubUserInfo merchant, int index) {
    final isSelected = homeMainController.currentUserInfoIndex.value == index;
    return HPTextButton(
      onPressed: () {
        homeMainController.updateCurrentUserInfoIndex(index);
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 12.h),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: isSelected
                ? ColorConstants.mainThemeColor
                : Colors.grey.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 1.sw - 100.w,
                  child: Text(
                    merchant.custShortName ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: ThemeConfig.boldBackText18.copyWith(fontSize: 16.sp),
                  ),
                ),
                SizedBox(height: 8.h),
                SizedBox(
                  width: 1.sw - 100.w,
                  child: Text(
                    merchant.custRegName ?? '',
                    style: ThemeConfig.norGrayText12.copyWith(fontSize: 13.sp),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: ColorConstants.mainThemeColor,
                size: 20.sp,
              ),
          ],
        ),
      ),
    );
  }

  /// 构建确认按钮
  Widget _buildConfirmButton(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 24.w,
        right: 24.w,
        top: 12.h,
        bottom: MediaQuery.of(context).padding.bottom + 8.h,
      ),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Color(0xFFE5E6EB),
            width: 1,
          ),
        ),
      ),
      child: HPBorderButton(
        text: 'common_btn_confirm'.tr,
        onPressed: () async {
          SubUserInfo subUserInfo = homeMainController
              .merchantList[homeMainController.currentUserInfoIndex.value];
          bool flag = await homeMainController.switchUser(subUserInfo);
          if (flag) {
            // 跳转首页
            HPRouter.resolvePage(Routes.main,
                preventDuplicates: false, destroyBinding: true);
          }
        },
      ),
    );
  }
}
