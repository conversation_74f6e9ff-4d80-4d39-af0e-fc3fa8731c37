/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-19 17:20:55
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-19 18:00:31
 * @FilePath: /hwicc-mobile-flutter/lib/pages/home/<USER>/home_new_data_widget.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:auapp/pages/main/main_controller.dart';
import 'package:auapp/r.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/widgets/hp_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class HomeNewDataWidget extends StatelessWidget {
  HomeNewDataWidget({super.key});
  final MainController mainController = Get.find<MainController>();

  final List<Map<String, String>> funcList = [
    {
      "type": "1",
      "name": "home_transactions".tr,
      "tip": "home_transactions_tip".tr,
      "img": R.assetsImagesHomeFuncAccount,
      "route": Routes.transaction,
    },
    {
      "type": "2",
      "name": "home_settlements".tr,
      "tip": "home_settlements_tip".tr,
      "img": R.assetsImagesHomeFuncSettle,
      "route": Routes.settlement,
    },
  ];

  Widget _funcWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: funcList.map((e) {
        return HPTextButton(
          onPressed: () {
            if (e['route'] != null) {
              if (e['route'] == Routes.transaction) {
                mainController.switchTab(1);
              } else {
                Get.toNamed(e['route']!);
              }
            }
          },
          child: Container(
            width: ((1.sw - 40.w) / 2),
            height: 90.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(14.sp),
              color: const Color(0xFFF4F6FF),
            ),
            child: Stack(
              children: [
                Positioned(
                    bottom: 0,
                    right: 0,
                    width: 58.w,
                    height: 60.h,
                    child: Image.asset(
                      e["img"]!,
                      width: 48.w,
                      height: 48.w,
                    )),
                Positioned(
                    left: 12.w,
                    top: 12.h,
                    width: ((1.sw - 40.w) / 2) - 60.w,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          e["name"]!,
                          style: ThemeConfig.norBackText12
                              .copyWith(fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          e["tip"]!,
                          style: ThemeConfig.norGrayText12,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ))
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.sp),
          topRight: Radius.circular(16.sp),
        ),
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 20.h, left: 16.w, right: 16.w),
            child: _funcWidget(),
          ),
        ],
      ),
    );
  }
}
