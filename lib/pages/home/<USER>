/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-16 11:00:31
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-23 17:07:15
 * @FilePath: /hwicc-mobile-flutter/lib/pages/home/<USER>
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/base/base_controller.dart';
import 'package:auapp/pages/transaction/model/transaction_model.dart';
import 'package:auapp/utils/hp_date_util.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

enum HomeSelectDateType { todayType, weekType, monthType, yearType }

class HomeMainController extends BaseController {
  HomeMainController();

  final RefreshController refreshController = RefreshController();

  // 时间范围
  final RxString beginDate = "".obs;
  final RxString endDate = "".obs;

  // 时间范围类型
  final Rx<HomeSelectDateType> currDateType = HomeSelectDateType.todayType.obs;

  // 交易时间
  final RxList<TransactionStatisticDayModel> transactionTimeList =
      <TransactionStatisticDayModel>[].obs;
  // 交易汇总
  final Rx<TransactionInfoSummaryModel> transactionInfoSummaryModel =
      TransactionInfoSummaryModel().obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();

    switchDateType(HomeSelectDateType.todayType);
  }

  @override
  void onShow() {
    // TODO: implement onShow
    super.onShow();

    switchDateType(HomeSelectDateType.todayType);
  }

  // 切换日期
  void switchDateType(HomeSelectDateType dateType) {
    currDateType.value = dateType;
    endDate.value = HPDateUtil.dateFormat(DateTime.now(), format: 'yyyy-MM-dd');
    switch (dateType) {
      case HomeSelectDateType.todayType:
        beginDate.value = endDate.value;
        break;
      case HomeSelectDateType.weekType:
        beginDate.value = HPDateUtil.dateFormat(
            HPDateUtil.getFirstDayOfWeek(DateTime.now()),
            format: 'yyyy-MM-dd');
        break;
      case HomeSelectDateType.monthType:
        beginDate.value = HPDateUtil.dateFormat(
            HPDateUtil.getFirstDayOfMonth(DateTime.now()),
            format: 'yyyy-MM-dd');
        break;
      case HomeSelectDateType.yearType:
        beginDate.value = HPDateUtil.dateFormat(
            HPDateUtil.getFirstDayOfYear(DateTime.now()),
            format: 'yyyy-MM-dd');
        break;
      default:
    }
    requestTransactionInfoSummaryData();
  }

  // 下拉刷新当前日期的汇总和列表数据
  refreshCurrDateData() async {
    // getUserSwitchList();
    switchDateType(currDateType.value);
  }

  // 获取看板的汇总数据
  requestTransactionInfoSummaryData() async {
    try {
      HPResponse response = await HttpRequest.post(
          ApiConstants.transactionInfoSummaryUrl,
          customBaseUrl: ApiConstants.baseTransactionUrl,
          params: {
            "beginDateTime": "$beginDate 00:00:00",
            "endDateTime": "$endDate 23:59:59",
          });
      if (response.isSuccess) {
        final res = response.data;
        if (res != null) {
          transactionInfoSummaryModel.value =
              TransactionInfoSummaryModel.fromJson(res);
        }
      } else {
        HPLoading.showToast(response.msg);
      }
    } catch (e) {
      HPLoading.showToast(e.toString());
    } finally {
      refreshController.refreshCompleted();
    }
  }
}
