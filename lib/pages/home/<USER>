import 'package:auapp/models/HPUserInfoSingleton.dart';
import 'package:auapp/pages/home/<USER>/home_data_widget.dart';
import 'package:auapp/pages/home/<USER>/home_new_data_widget.dart';
import 'package:auapp/pages/home/<USER>/switch_merchant_sheet.dart';
import 'package:auapp/pages/main/main_controller.dart';
import 'package:auapp/r.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/widgets/hp_text_button.dart';
import 'package:auapp/widgets/smart_refresher_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:auapp/pages/home/<USER>';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class HomeMainPage extends GetView<HomeMainController> {
  HomeMainPage({super.key});

  final MainController mainController = Get.find<MainController>();
  /**
   * 顶部商户名称切换
   */
  Widget _merNameWidget(BuildContext context) {
    return Obx(() => HPTextButton(
          onPressed: () async {
            // 如果只有一个商户，不显示切换界面
            if (!mainController.hasMultipleMerchants) {
              return;
            }
            SwitchMerchantSheet.show();
          },
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Row(
              children: [
                Container(
                  constraints: BoxConstraints(maxWidth: 1.sw - 80.w),
                  child: Text(
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    HPUserInfoSingleton().custShortName ?? '',
                    style: ThemeConfig.boldBackText18,
                  ),
                ),
                Visibility(
                  visible: controller.merchantList.length > 1,
                  child: Image.asset(
                    R.assetsImagesHomeMerDropdown,
                    width: 24.w,
                    height: 24.w,
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  List<Widget> _topBackgroundWidget(BuildContext context) {
    return [
      Positioned(
        top: 82.h,
        right: 0,
        width: 190.w,
        height: 272.h,
        child: Container(
          decoration: BoxDecoration(
              image: DecorationImage(
            image: AssetImage(R.assetsImagesHomeBackgroundMerImg),
            fit: BoxFit.cover,
          )),
        ),
      ),
      Positioned(
        top: MediaQuery.of(context).padding.top + 8.h,
        left: 0,
        right: 0,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [_merNameWidget(context), HomeDataWidget()],
        ),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
    );
    return Scaffold(
      appBar: null,
      body: Stack(
        children: [
          Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: 375.h,
              child: Container(
                decoration: BoxDecoration(
                    image: DecorationImage(
                  image: AssetImage(R.assetsImagesHomeBackgroundImg),
                  fit: BoxFit.cover,
                )),
              )),
          SmartRefresher(
            controller: controller.refreshController,
            enablePullDown: true,
            enablePullUp: false,
            header: const HPRefreshHeader(),
            onRefresh: () {
              controller.refreshCurrDateData();
            },
            child: Stack(children: [
              ..._topBackgroundWidget(context),
              Positioned(
                top: 336.h,
                left: 0,
                right: 0,
                child: HomeNewDataWidget(),
              ),
            ]),
          )
        ],
      ),
    );
  }
}
