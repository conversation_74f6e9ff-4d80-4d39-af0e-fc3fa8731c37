/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-16 11:00:31
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-23 17:06:06
 * @FilePath: /hwicc-mobile-flutter/lib/pages/home/<USER>
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:auapp/base/base_binding.dart';
import 'package:auapp/pages/home/<USER>';
import 'package:get/get.dart';

class HomeMainBinding implements BaseBinding {
  @override
  void dependencies() {
    Get.lazyPut<HomeMainController>(() => HomeMainController(), fenix: true);
  }
}
