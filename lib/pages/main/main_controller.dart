/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-16 11:00:31
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-23 17:45:58
 * @FilePath: /hwicc-mobile-flutter/lib/pages/main/main_controller.dart
 * @Description: 主控制器 - 全局状态管理中心，负责商户管理、tab切换等全局功能
 */
import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/models/HPUserInfoSingleton.dart';
import 'package:auapp/pages/home/<USER>';
import 'package:auapp/pages/main/main_provider.dart';
import 'package:auapp/pages/merchantSelect/models/merchant_select_data.dart';
import 'package:auapp/pages/merchantSelect/models/sub_user_info.dart';
import 'package:auapp/pages/merchantSelect/models/switch_data.dart';
import 'package:auapp/pages/mine/mine_main_controller.dart';
import 'package:auapp/pages/reports/reports_main_controller.dart';
import 'package:auapp/pages/transaction/transaction_main_controller.dart';
import 'package:auapp/pub/constants/storage_constants.dart';
import 'package:auapp/services/app_info_service.dart';
import 'package:auapp/services/storage_service.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:get/get.dart';
import 'package:auapp/pages/main/tabs/tab_enums.dart';

class MainController extends GetxController {
  final MainProvider apiRepository;
  MainController({required this.apiRepository});

  // ==================== Tab 管理 ====================
  var currentTab = MainTabs.home.obs;

  // ==================== 商户管理 ====================
  /// 商户列表
  final RxList<SubUserInfo> merchantList = <SubUserInfo>[].obs;

  /// 当前选中商户的索引
  final RxInt currentMerchantIndex = 0.obs;

  /// 当前选中的商户信息
  SubUserInfo? get currentMerchant {
    if (merchantList.isEmpty ||
        currentMerchantIndex.value < 0 ||
        currentMerchantIndex.value >= merchantList.length) {
      return null;
    }
    return merchantList[currentMerchantIndex.value];
  }

  /// 是否有多个商户
  bool get hasMultipleMerchants => merchantList.length > 1;

  @override
  void onInit() async {
    super.onInit();

    /// 获取操作员扩展信息
    await initHPUserInfoSingleton();

    /// 获取商户列表
    await loadMerchantList();
  }

  @override
  onClose() {
    super.onClose();

    destroyBinding();
  }

  destroyBinding() {
    // // 清理所有 tab 页的控制器
    // if (Get.isRegistered<HomeMainController>()) {
    //   Get.delete<HomeMainController>(force: true);
    // }
    // if (Get.isRegistered<TransactionMainController>()) {
    //   Get.delete<TransactionMainController>(force: true);
    // }
    // if (Get.isRegistered<ReportsMainController>()) {
    //   Get.delete<ReportsMainController>(force: true);
    // }
    // if (Get.isRegistered<MineMainController>()) {
    //   Get.delete<MineMainController>(force: true);
    // }
  }

  @override
  Future<void> onReady() async {
    // TODO: implement onReady
    super.onReady();
  }

  /// 初始化操作员信息
  Future<void> initHPUserInfoSingleton() async {
    // 初始化用户单例
    await HPUserInfoSingleton().init();
  }

  void switchTab(int index) {
    var tab = _getCurrentTab(index);
    currentTab.value = tab;
  }

  int getCurrentIndex(MainTabs tab) {
    switch (tab) {
      case MainTabs.home:
        return 0;
      case MainTabs.transaction:
        return 1;
      case MainTabs.report:
        return 2;
      case MainTabs.mine:
        return 3;
    }
  }

  MainTabs _getCurrentTab(int index) {
    switch (index) {
      case 0:
        return MainTabs.home;
      case 1:
        return MainTabs.transaction;
      case 2:
        return MainTabs.report;
      case 3:
        return MainTabs.mine;
      default:
        return MainTabs.home;
    }
  }

  // ==================== 商户管理方法 ====================

  /// 加载商户列表
  Future<bool> loadMerchantList() async {
    try {
      HPResponse response =
          await HttpRequest.get(ApiConstants.getUserSwitchListUrl);
      if (response.isSuccess) {
        final res = MerchantSelectListData.fromJson(response.data);
        final List<SubUserInfo> subSelectList = res.subUserInfoList ?? [];
        if (subSelectList.isNotEmpty) {
          merchantList.assignAll(subSelectList);
          // 设置当前商户索引
          _updateCurrentMerchantIndex();
        }
        return true;
      } else {
        HPLoading.showToast(response.msg);
      }
    } catch (e) {
      HPLoading.showToast(e.toString());
    }
    return false;
  }

  /// 更新当前商户索引
  void _updateCurrentMerchantIndex() {
    final currentSubUserId = HPUserInfoSingleton().subUserId;
    final index = merchantList
        .indexWhere((element) => element.subUserId == currentSubUserId);
    currentMerchantIndex.value = index >= 0 ? index : 0;
  }

  /// 切换商户
  Future<bool> switchMerchant(SubUserInfo subUserInfo) async {
    try {
      final AppInfoService appInfoService = Get.find<AppInfoService>();
      Map<String, dynamic> appInfoJson = appInfoService.getAppInfoJson();

      HPResponse response = await HttpRequest.post(ApiConstants.switchUserUrl,
          params: {
            'sub_user_id': subUserInfo.subUserId,
            'app_info': appInfoJson
          });

      if (response.isSuccess) {
        final SwitchData switchData = SwitchData.fromJson(response.data);
        if (switchData.hfToken != null) {
          // 保存token
          StorageService.setString(StorageConstants.token, switchData.hfToken!);

          // 更新用户信息
          await HPUserInfoSingleton().updateUserInfo(subUserInfo: subUserInfo);

          // 更新当前商户索引
          _updateCurrentMerchantIndex();

          // 通知所有tab刷新数据
          await _notifyTabsRefresh();

          return true;
        }
      } else {
        HPLoading.showToast(response.msg);
      }
    } catch (e) {
      HPLoading.showToast(e.toString());
    }
    return false;
  }

  /// 通知所有tab刷新数据
  Future<void> _notifyTabsRefresh() async {
    // 通知Home页面刷新
    if (Get.isRegistered<HomeMainController>()) {
      final homeController = Get.find<HomeMainController>();
      homeController.refreshCurrDateData();
    }

    // 通知Transaction页面刷新
    if (Get.isRegistered<TransactionMainController>()) {
      final transactionController = Get.find<TransactionMainController>();
      transactionController.reloadData();
    }

    // 通知Mine页面刷新
    if (Get.isRegistered<MineMainController>()) {
      final mineController = Get.find<MineMainController>();
      mineController.fetchSettlementData();
    }

    // Reports页面暂时没有需要刷新的数据
  }

  /// 获取当前商户名称
  String getCurrentMerchantName() {
    return currentMerchant?.custShortName ??
        HPUserInfoSingleton().custShortName ??
        '';
  }

  /// 获取当前商户完整名称
  String getCurrentMerchantFullName() {
    return currentMerchant?.custRegName ?? '';
  }
}
