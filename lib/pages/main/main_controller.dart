/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-16 11:00:31
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-23 17:45:58
 * @FilePath: /hwicc-mobile-flutter/lib/pages/main/main_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:auapp/models/HPUserInfoSingleton.dart';
import 'package:auapp/pages/home/<USER>';
import 'package:auapp/pages/main/main_provider.dart';
import 'package:auapp/pages/mine/mine_main_controller.dart';
import 'package:auapp/pages/reports/reports_main_controller.dart';
import 'package:auapp/pages/transaction/transaction_main_controller.dart';
import 'package:get/get.dart';
import 'package:auapp/pages/main/tabs/tab_enums.dart';

class MainController extends GetxController {
  final MainProvider apiRepository;
  MainController({required this.apiRepository});

  var currentTab = MainTabs.home.obs;

  @override
  void onInit() async {
    super.onInit();

    /// 获取操作员扩展信息
    await initHPUserInfoSingleton();
  }

  @override
  onClose() {
    super.onClose();

    destroyBinding();
  }

  destroyBinding() {
    // 清理所有 tab 页的控制器
    if (Get.isRegistered<HomeMainController>()) {
      Get.delete<HomeMainController>(force: true);
    }
    if (Get.isRegistered<TransactionMainController>()) {
      Get.delete<TransactionMainController>(force: true);
    }
    if (Get.isRegistered<ReportsMainController>()) {
      Get.delete<ReportsMainController>(force: true);
    }
    if (Get.isRegistered<MineMainController>()) {
      Get.delete<MineMainController>(force: true);
    }
  }

  @override
  Future<void> onReady() async {
    // TODO: implement onReady
    super.onReady();
  }

  /// 初始化操作员信息
  Future<void> initHPUserInfoSingleton() async {
    // 初始化用户单例
    await HPUserInfoSingleton().init();
  }

  void switchTab(int index) {
    var tab = _getCurrentTab(index);
    currentTab.value = tab;
  }

  int getCurrentIndex(MainTabs tab) {
    switch (tab) {
      case MainTabs.home:
        return 0;
      case MainTabs.transaction:
        return 1;
      case MainTabs.report:
        return 2;
      case MainTabs.mine:
        return 3;
    }
  }

  MainTabs _getCurrentTab(int index) {
    switch (index) {
      case 0:
        return MainTabs.home;
      case 1:
        return MainTabs.transaction;
      case 2:
        return MainTabs.report;
      case 3:
        return MainTabs.mine;
      default:
        return MainTabs.home;
    }
  }
}
