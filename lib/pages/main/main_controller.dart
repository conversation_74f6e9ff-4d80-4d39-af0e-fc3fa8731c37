/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-16 11:00:31
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-23 17:45:58
 * @FilePath: /hwicc-mobile-flutter/lib/pages/main/main_controller.dart
 * @Description: 主控制器 - 全局状态管理中心，负责商户管理、tab切换等全局功能
 */
import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/models/HPUserInfoSingleton.dart';
import 'package:auapp/pages/home/<USER>';
import 'package:auapp/pages/main/main_provider.dart';
import 'package:auapp/pages/merchantSelect/models/merchant_select_data.dart';
import 'package:auapp/pages/merchantSelect/models/sub_user_info.dart';
import 'package:auapp/pages/merchantSelect/models/switch_data.dart';
import 'package:auapp/pages/mine/mine_main_controller.dart';
import 'package:auapp/pages/reports/reports_main_controller.dart';
import 'package:auapp/pages/transaction/transaction_main_controller.dart';
import 'package:auapp/pub/constants/storage_constants.dart';
import 'package:auapp/services/app_info_service.dart';
import 'package:auapp/services/storage_service.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:get/get.dart';
import 'package:auapp/pages/main/tabs/tab_enums.dart';

class MainController extends GetxController {
  final MainProvider apiRepository;
  MainController({required this.apiRepository});

  // ==================== Tab 管理 ====================
  var currentTab = MainTabs.home.obs;

  // Tab 切换防抖
  DateTime? _lastSwitchTime;
  static const _switchDebounceMs = 300;

  // ==================== 商户管理 ====================
  /// 商户列表
  final RxList<SubUserInfo> merchantList = <SubUserInfo>[].obs;

  /// 当前选中商户的索引
  final RxInt currentMerchantIndex = 0.obs;

  /// 当前选中的商户信息
  SubUserInfo? get currentMerchant {
    if (merchantList.isEmpty ||
        currentMerchantIndex.value < 0 ||
        currentMerchantIndex.value >= merchantList.length) {
      return null;
    }
    return merchantList[currentMerchantIndex.value];
  }

  /// 是否有多个商户
  bool get hasMultipleMerchants => merchantList.length > 1;

  @override
  void onInit() async {
    super.onInit();

    /// 获取操作员扩展信息
    await initHPUserInfoSingleton();

    /// 获取商户列表
    await loadMerchantList();
  }

  @override
  onClose() async {
    super.onClose();

    await destroyBinding();
  }

  Future<void> destroyBinding() async {
    // 安全清理所有 tab 页的控制器
    await _safeDisposeController<HomeMainController>();
    await _safeDisposeController<TransactionMainController>();
    await _safeDisposeController<ReportsMainController>();
    await _safeDisposeController<MineMainController>();
  }

  /// 安全地销毁控制器，确保 RefreshController 正确释放
  Future<void> _safeDisposeController<T extends GetxController>() async {
    if (Get.isRegistered<T>()) {
      try {
        final controller = Get.find<T>();

        // 如果控制器有 RefreshController，先停止刷新操作
        if (controller is HomeMainController) {
          controller.refreshController.refreshCompleted();
        } else if (controller is TransactionMainController) {
          controller.refreshController.refreshCompleted();
          controller.dayRefreshController.refreshCompleted();
        }

        // 等待一帧，确保刷新操作完成
        await Future.delayed(const Duration(milliseconds: 50));

        // 安全删除控制器
        Get.delete<T>(force: true);
      } catch (e) {
        // 如果出错，仍然尝试删除
        Get.delete<T>(force: true);
      }
    }
  }

  @override
  Future<void> onReady() async {
    // TODO: implement onReady
    super.onReady();
  }

  /// 初始化操作员信息
  Future<void> initHPUserInfoSingleton() async {
    // 初始化用户单例
    await HPUserInfoSingleton().init();
  }

  void switchTab(int index) {
    // 防抖：避免快速连续切换导致的问题
    final now = DateTime.now();
    if (_lastSwitchTime != null &&
        now.difference(_lastSwitchTime!).inMilliseconds < _switchDebounceMs) {
      return;
    }
    _lastSwitchTime = now;

    var tab = _getCurrentTab(index);
    if (currentTab.value == tab) {
      return; // 如果是同一个tab，不需要切换
    }

    currentTab.value = tab;

    // 延迟执行刷新，确保UI切换完成
    Future.delayed(const Duration(milliseconds: 100), () {
      _notifyTabsRefresh();
    });
  }

  int getCurrentIndex(MainTabs tab) {
    switch (tab) {
      case MainTabs.home:
        return 0;
      case MainTabs.transaction:
        return 1;
      // case MainTabs.report:
      //   return 2;
      case MainTabs.mine:
        return 2;
      default:
        return 0;
    }
  }

  MainTabs _getCurrentTab(int index) {
    switch (index) {
      case 0:
        return MainTabs.home;
      case 1:
        return MainTabs.transaction;
      // case 2:
      //   return MainTabs.report;
      case 2:
        return MainTabs.mine;
      default:
        return MainTabs.home;
    }
  }

  // ==================== 商户管理方法 ====================

  /// 加载商户列表
  Future<bool> loadMerchantList() async {
    try {
      HPResponse response =
          await HttpRequest.get(ApiConstants.getUserSwitchListUrl);
      if (response.isSuccess) {
        final res = MerchantSelectListData.fromJson(response.data);
        final List<SubUserInfo> subSelectList = res.subUserInfoList ?? [];
        if (subSelectList.isNotEmpty) {
          merchantList.assignAll(subSelectList);
          // 设置当前商户索引
          _updateCurrentMerchantIndex();
        }
        return true;
      } else {
        HPLoading.showToast(response.msg);
      }
    } catch (e) {
      HPLoading.showToast(e.toString());
    }
    return false;
  }

  /// 更新当前商户索引
  void _updateCurrentMerchantIndex() {
    final currentSubUserId = HPUserInfoSingleton().subUserId;
    final index = merchantList
        .indexWhere((element) => element.subUserId == currentSubUserId);
    currentMerchantIndex.value = index >= 0 ? index : 0;
  }

  /// 切换商户
  Future<bool> switchMerchant(SubUserInfo subUserInfo) async {
    try {
      final AppInfoService appInfoService = Get.find<AppInfoService>();
      Map<String, dynamic> appInfoJson = appInfoService.getAppInfoJson();

      HPResponse response = await HttpRequest.post(ApiConstants.switchUserUrl,
          params: {
            'sub_user_id': subUserInfo.subUserId,
            'app_info': appInfoJson
          });

      if (response.isSuccess) {
        final SwitchData switchData = SwitchData.fromJson(response.data);
        if (switchData.hfToken != null) {
          // 保存token
          StorageService.setString(StorageConstants.token, switchData.hfToken!);

          // 更新用户信息
          await HPUserInfoSingleton().updateUserInfo(subUserInfo: subUserInfo);

          // 更新当前商户索引
          _updateCurrentMerchantIndex();

          // 通知所有tab刷新数据
          await _notifyTabsRefresh();

          return true;
        }
      } else {
        HPLoading.showToast(response.msg);
      }
    } catch (e) {
      HPLoading.showToast(e.toString());
    }
    return false;
  }

  /// 通知所有tab刷新数据
  Future<void> _notifyTabsRefresh() async {
    try {
      // 延迟执行，确保UI切换完成
      await Future.delayed(const Duration(milliseconds: 100));

      switch (currentTab.value) {
        case MainTabs.home:
          await _safeRefreshHome();
          break;
        case MainTabs.transaction:
          await _safeRefreshTransaction();
          break;
        case MainTabs.mine:
          await _safeRefreshMine();
          break;
        case MainTabs.report:
          // Reports页面暂时没有需要刷新的数据
          break;
      }
    } catch (e) {
      // 使用HPLoading显示错误，而不是print
      HPLoading.showToast('刷新数据时出错');
    }
  }

  /// 安全刷新Home页面
  Future<void> _safeRefreshHome() async {
    if (Get.isRegistered<HomeMainController>()) {
      try {
        final homeController = Get.find<HomeMainController>();
        // 检查RefreshController状态
        if (!homeController.refreshController.isRefresh) {
          homeController.refreshCurrDateData();
        }
      } catch (e) {
        // 忽略刷新错误
      }
    }
  }

  /// 安全刷新Transaction页面
  Future<void> _safeRefreshTransaction() async {
    if (Get.isRegistered<TransactionMainController>()) {
      try {
        final transactionController = Get.find<TransactionMainController>();
        // 检查RefreshController状态
        if (!transactionController.refreshController.isRefresh &&
            !transactionController.dayRefreshController.isRefresh) {
          transactionController.reloadData(needAnimate: false);
        }
      } catch (e) {
        // 忽略刷新错误
      }
    }
  }

  /// 安全刷新Mine页面
  Future<void> _safeRefreshMine() async {
    if (Get.isRegistered<MineMainController>()) {
      try {
        final mineController = Get.find<MineMainController>();
        mineController.fetchSettlementData();
      } catch (e) {
        // 忽略刷新错误
      }
    }
  }

  /// 获取当前商户名称
  String getCurrentMerchantName() {
    return currentMerchant?.custShortName ??
        HPUserInfoSingleton().custShortName ??
        '';
  }

  /// 获取当前商户完整名称
  String getCurrentMerchantFullName() {
    return currentMerchant?.custRegName ?? '';
  }
}
