import 'package:auapp/pages/home/<USER>';
import 'package:auapp/pages/home/<USER>';
import 'package:auapp/pages/home/<USER>';
import 'package:auapp/pages/mine/mine_main_binding.dart';
import 'package:auapp/pages/mine/mine_main_controller.dart';
import 'package:auapp/pages/mine/mine_main_page.dart';
import 'package:auapp/pages/reports/reports_main_binding.dart';
import 'package:auapp/pages/reports/reports_main_controller.dart';
import 'package:auapp/pages/reports/reports_main_page.dart';
import 'package:auapp/pages/transaction/transaction_main_binding.dart';
import 'package:auapp/pages/transaction/transaction_main_controller.dart';
import 'package:auapp/pages/transaction/transaction_main_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:auapp/pages/main/main_controller.dart';
import 'package:auapp/pages/main/tabs/tab_enums.dart';

import '../../pub/constants/color_constants.dart';
import '../../r.dart';
import '../../theme/theme_config.dart';

class _BottomNavItem {
  final String labelKey;
  final String selectedIcon;
  final String unselectedIcon;
  final MainTabs tabValue;

  _BottomNavItem({
    required this.labelKey,
    required this.selectedIcon,
    required this.unselectedIcon,
    required this.tabValue,
  });
}

class MainPage extends GetView<MainController> {
  const MainPage({super.key});

  static final List<_BottomNavItem> _navItems = [
    _BottomNavItem(
      labelKey: "main_tab_title_home".tr,
      selectedIcon: R.assetsImagesTabHomeSel,
      unselectedIcon: R.assetsImagesTabHomeUnsel,
      tabValue: MainTabs.home,
    ),
    _BottomNavItem(
      labelKey: "main_tab_title_transaction".tr,
      selectedIcon: R.assetsImagesTabTransactionSel,
      unselectedIcon: R.assetsImagesTabTransactionUnsel,
      tabValue: MainTabs.transaction,
    ),
    // _BottomNavItem(
    //   labelKey: "main_tab_title_reports".tr,
    //   selectedIcon: R.assetsImagesTabReportsSel,
    //   unselectedIcon: R.assetsImagesTabReportsUnsel,
    //   tabValue: MainTabs.report,
    // ),
    _BottomNavItem(
      labelKey: "main_tab_title_mine".tr,
      selectedIcon: R.assetsImagesTabMineSel,
      unselectedIcon: R.assetsImagesTabMineUnsel,
      tabValue: MainTabs.mine,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return PopScope(child: Obx(() => _buildWidget()));
  }

  Widget _buildCustomBottomNavigationBar(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final int itemCount = _navItems.length;
    final double itemWidth = screenWidth / itemCount;
    final double indicatorHeight = 1.5; // 指示器高度
    final double indicatorWidth = itemWidth * 0.4; // 指示器宽度
    final double navBarHeight =
        kBottomNavigationBarHeight + 8.0; // 稍微增加一点高度以容纳指示器和文本

    final int currentIndex =
        controller.getCurrentIndex(controller.currentTab.value);

    return Container(
      height: navBarHeight,
      decoration: BoxDecoration(
        color: Colors.white, // 或者 Theme.of(context).canvasColor
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, -1), // 顶部阴影
          ),
        ],
      ),
      child: Stack(
        children: [
          // 滑动指示器
          AnimatedPositioned(
            duration: const Duration(milliseconds: 250), // 动画时长
            curve: Curves.easeOutCubic, // 动画曲线
            left: currentIndex * itemWidth +
                (itemWidth / 2) -
                (indicatorWidth / 2), // 居中指示器
            bottom: 1, // 将指示器放在图标下方，文本上方
            height: indicatorHeight,
            width: indicatorWidth, // 指示器宽度，例如 Tab 宽度的 30%
            child: Container(
                decoration: BoxDecoration(
              color: const Color(0xff0FACF5), // 指示器颜色
              borderRadius: BorderRadius.circular(indicatorHeight / 2), // 圆角
            )),
          ),
          // Tab 项
          Row(
            children: _navItems.asMap().entries.map((entry) {
              int index = entry.key;
              _BottomNavItem item = entry.value;
              bool isSelected = currentIndex == index;

              return Expanded(
                child: InkWell(
                  onTap: () => controller.switchTab(index),
                  // splashColor: Colors.transparent, // 去除水波纹效果
                  // highlightColor: Colors.transparent, // 去除高亮效果
                  child: Container(
                    alignment: Alignment.center,
                    height: navBarHeight,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Image.asset(
                          isSelected ? item.selectedIcon : item.unselectedIcon,
                          width: UiSize.setSize(20),
                          height: UiSize.setSize(20),
                        ),
                        SizedBox(height: 5.h), // 图标和文字的间距
                        Text(
                          item.labelKey.tr,
                          style: TextStyle(
                            fontSize: 10.sp, // 统一文字大小
                            fontWeight: FontWeight.normal,
                            color: isSelected
                                ? const Color(0xff0FACF5)
                                : const Color(0xffA7ADB0),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildWidget() {
    return Scaffold(
      body: Center(
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 300), // 您可以调整动画时长
          child: SizedBox(
            // 使用 SizedBox 包裹并提供 key，这对于 AnimatedSwitcher 正确识别子 Widget 很重要
            key: ValueKey<MainTabs>(controller.currentTab.value),
            child: _buildContent(controller.currentTab.value),
          ),
          transitionBuilder: (Widget child, Animation<double> animation) {
            // 这是淡入淡出效果，您可以替换为其他动画效果
            return FadeTransition(opacity: animation, child: child);
          },
        ),
      ),
      bottomNavigationBar: _buildCustomBottomNavigationBar(Get.context!),
    );
  }

  Widget _buildContent(MainTabs tab) {
    switch (tab) {
      case MainTabs.home:
        if (!Get.isRegistered<HomeMainController>()) {
          HomeMainBinding().dependencies();
        }
        return HomeMainPage();

      case MainTabs.transaction:
        if (!Get.isRegistered<TransactionMainController>()) {
          TransactionMainBinding().dependencies();
        }
        return TransactionMainPage();

      // case MainTabs.report:
      //   if (!Get.isRegistered<ReportsMainController>()) {
      //     ReportsMainBinding().dependencies();
      //   }
      //   return const ReportsMainPage();

      case MainTabs.mine:
        if (!Get.isRegistered<MineMainController>()) {
          MineMainBinding().dependencies();
        }
        return MineMainPage();

      default:
        if (!Get.isRegistered<HomeMainController>()) {
          HomeMainBinding().dependencies();
        }
        return HomeMainPage();
    }
  }

  // BottomNavigationBarItem _buildNavigationBarItem(
  //     String label, String iconName) {
  //   return BottomNavigationBarItem(
  //     icon: Image.asset(
  //       iconName,
  //       width: UiSize.setSize(20),
  //       height: UiSize.setSize(20),
  //     ),
  //     label: label,
  //   );
  // }
}
