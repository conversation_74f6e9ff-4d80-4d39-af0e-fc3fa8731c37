import 'package:auapp/pages/home/<USER>';
import 'package:auapp/pages/home/<USER>';
import 'package:auapp/pages/home/<USER>';
import 'package:auapp/pages/mine/mine_main_binding.dart';
import 'package:auapp/pages/mine/mine_main_controller.dart';
import 'package:auapp/pages/mine/mine_main_page.dart';
import 'package:auapp/pages/reports/reports_main_binding.dart';
import 'package:auapp/pages/reports/reports_main_controller.dart';
import 'package:auapp/pages/reports/reports_main_page.dart';
import 'package:auapp/pages/transaction/transaction_main_binding.dart';
import 'package:auapp/pages/transaction/transaction_main_controller.dart';
import 'package:auapp/pages/transaction/transaction_main_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:auapp/pages/main/main_controller.dart';
import 'package:auapp/pages/main/tabs/tab_enums.dart';

import '../../pub/constants/color_constants.dart';
import '../../r.dart';
import '../../theme/theme_config.dart';

class MainPage extends GetView<MainController> {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(child: Obx(() => _buildWidget()));
  }

  Widget _buildWidget() {
    return Scaffold(
      body: Center(
        child: _buildContent(controller.currentTab.value),
      ),
      bottomNavigationBar: BottomNavigationBar(
        items: [
          _buildNavigationBarItem(
            "main_tab_title_home".tr,
            MainTabs.home == controller.currentTab.value
                ? R.assetsImagesTabHomeSel
                : R.assetsImagesTabHomeUnsel,
          ),
          _buildNavigationBarItem(
            "main_tab_title_transaction".tr,
            MainTabs.transaction == controller.currentTab.value
                ? R.assetsImagesTabTransactionSel
                : R.assetsImagesTabTransactionUnsel,
          ),
          _buildNavigationBarItem(
            "main_tab_title_reports".tr,
            MainTabs.report == controller.currentTab.value
                ? R.assetsImagesTabReportsSel
                : R.assetsImagesTabReportsUnsel,
          ),
          _buildNavigationBarItem(
            "main_tab_title_mine".tr,
            MainTabs.mine == controller.currentTab.value
                ? R.assetsImagesTabMineSel
                : R.assetsImagesTabMineUnsel,
          )
        ],
        type: BottomNavigationBarType.fixed,
        unselectedItemColor: ColorConstants.black,
        currentIndex: controller.getCurrentIndex(controller.currentTab.value),
        selectedItemColor: ColorConstants.black,
        selectedLabelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
        onTap: (index) => controller.switchTab(index),
      ),
    );
  }

  Widget _buildContent(MainTabs tab) {
    switch (tab) {
      case MainTabs.home:
        if (!Get.isRegistered<HomeMainController>()) {
          HomeMainBinding().dependencies();
        }
        return HomeMainPage();

      case MainTabs.transaction:
        if (!Get.isRegistered<TransactionMainController>()) {
          TransactionMainBinding().dependencies();
        }
        return TransactionMainPage();

      case MainTabs.report:
        if (!Get.isRegistered<ReportsMainController>()) {
          ReportsMainBinding().dependencies();
        }
        return const ReportsMainPage();

      case MainTabs.mine:
        if (!Get.isRegistered<MineMainController>()) {
          MineMainBinding().dependencies();
        }
        return MineMainPage();

      default:
        if (!Get.isRegistered<HomeMainController>()) {
          HomeMainBinding().dependencies();
        }
        return HomeMainPage();
    }
  }

  BottomNavigationBarItem _buildNavigationBarItem(
      String label, String iconName) {
    return BottomNavigationBarItem(
      icon: Image.asset(
        iconName,
        width: UiSize.setSize(20),
        height: UiSize.setSize(20),
      ),
      label: label,
    );
  }
}
