/*
 * ProjectName：UaApp
 * FilePath：lib/pages/main/models
 * FileName：main_login_operator_info
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/26 14:00:03
 */

import 'package:json_annotation/json_annotation.dart';

part 'main_login_operator_info.g.dart';

@JsonSerializable()
class MainLoginOperatorInfo {
  /// 子用户编号
  final String? uap_sub_user_id;

  /// 用户编号
  final String? uap_user_id;

  /// 操作员类型(admin-管理员 other-操作员)
  final String? operator_role;

  /// 用户名称
  final String? user_name;

  /// 邮箱
  final String? email;

  /// 区号
  final String? phone_area_code;

  /// 手机号
  final String? phone;

  /// 登录密码是否设置(Y-是 N-否)
  final String? set_pwd_flag;

  /// 交易密码是否设置(Y-是 N-否)
  final String? set_trade_pwd_flag;

  /// 商户/服务商客户号(uap客户号)
  final String? org_cust_id;

  /// 商户/服务商全称(uap客户全称)
  final String? full_name;

  /// 商户/服务商简称(uap客户简称)
  final String? short_name;

  /// 菜单列表
  final List<MenuListInfo>? menu_lists;

  final List<MenuPathInfoVO>? menu_path_list;

  MainLoginOperatorInfo({
    this.uap_sub_user_id,
    this.uap_user_id,
    this.operator_role,
    this.user_name,
    this.email,
    this.phone_area_code,
    this.phone,
    this.set_pwd_flag,
    this.set_trade_pwd_flag,
    this.org_cust_id,
    this.full_name,
    this.short_name,
    this.menu_lists,
    this.menu_path_list,
  });

  factory MainLoginOperatorInfo.fromJson(Map<String, dynamic> json) => _$MainLoginOperatorInfoFromJson(json);
  Map<String, dynamic> toJson() => _$MainLoginOperatorInfoToJson(this);
}

@JsonSerializable()
class MenuPathInfoVO {
  /// 路由path
  final String? path;

  /// pathName
  final String? name;

  /// 是否隐藏 true/false
  final bool? hidden;

  /// 链接方式
  final String? pathType;

  /// 本地路由地址
  final String? componentPath;

  /// 层级
  final String? level;

  /// 菜单ID
  final String? outResId;

  /// 父级菜单ID
  final String? parentOutResId;

  /// meta
  final MenuMetaVO? meta;

  final List<MenuPathInfoVO>? children;

  MenuPathInfoVO({
    this.path,
    this.name,
    this.hidden,
    this.pathType,
    this.componentPath,
    this.level,
    this.outResId,
    this.parentOutResId,
    this.meta,
    this.children,
  });

  factory MenuPathInfoVO.fromJson(Map<String, dynamic> json) => _$MenuPathInfoVOFromJson(json);
  Map<String, dynamic> toJson() => _$MenuPathInfoVOToJson(this);
}


@JsonSerializable()
class MenuMetaVO {
  /// 菜单图标
  final String? icon;

  /// 菜单标题
  final String? title;

  /// 权限
  final String? permission;

  /// 是否缓存 true/false
  final bool? keepAlive;

  /// 高亮菜单
  final String? activeMenu;

  /// 菜单分组
  final String? fstMenuRoute;

  /// 二级菜单分组title
  final String? group;

  /// 二级菜单分组权限
  final String? groupPermission;

  MenuMetaVO({
    this.icon,
    this.title,
    this.permission,
    this.keepAlive,
    this.activeMenu,
    this.fstMenuRoute,
    this.group,
    this.groupPermission
  });

  factory MenuMetaVO.fromJson(Map<String, dynamic> json) => _$MenuMetaVOFromJson(json);
  Map<String, dynamic> toJson() => _$MenuMetaVOToJson(this);
}


@JsonSerializable()
class MenuListInfo {
  final String? belong_sys_id;
  final String? out_res_id;
  final String? parent_id;
  final String? parent_ids;
  final String? name;
  final int? sort;
  final String? href;
  final String? icon;
  final String? level;
  final String? kind;
  final String? permission;
  final String? remark;
  final List<MenuListInfo>? sub;

  MenuListInfo({
    this.belong_sys_id,
    this.out_res_id,
    this.parent_id,
    this.parent_ids,
    this.name,
    this.sort,
    this.href,
    this.icon,
    this.level,
    this.kind,
    this.permission,
    this.remark,
    this.sub,
  });

  factory MenuListInfo.fromJson(Map<String, dynamic> json) => _$MenuListInfoFromJson(json);
  Map<String, dynamic> toJson() => _$MenuListInfoToJson(this);
}

