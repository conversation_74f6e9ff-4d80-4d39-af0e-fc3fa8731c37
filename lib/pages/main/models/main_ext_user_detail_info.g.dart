// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'main_ext_user_detail_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MainExtUserDetailInfo _$MainExtUserDetailInfoFromJson(
        Map<String, dynamic> json) =>
    MainExtUserDetailInfo(
      id: json['id'] as String?,
      login_name: json['login_name'] as String?,
      user_type: json['user_type'] as String?,
      phone_mask: json['phone_mask'] as String?,
      ent_org_admin: json['ent_org_admin'] as String?,
      is_org_admin: json['is_org_admin'] as String?,
      huifu_id: json['huifu_id'] as String?,
      role_ids: json['role_ids'] as String?,
      sub_user_id: json['sub_user_id'] as String?,
      sub_user_type: json['sub_user_type'] as String?,
      user_id: json['user_id'] as String?,
      name: json['name'] as String?,
      email: json['email'] as String?,
      phone_area_code: json['phone_area_code'] as String?,
      phone: json['phone'] as String?,
      is_active: json['is_active'] as String?,
      is_admin: json['is_admin'] as String?,
      refund_permission: json['refund_permission'] as String?,
    );

Map<String, dynamic> _$MainExtUserDetailInfoToJson(
        MainExtUserDetailInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'login_name': instance.login_name,
      'user_type': instance.user_type,
      'phone_mask': instance.phone_mask,
      'ent_org_admin': instance.ent_org_admin,
      'is_org_admin': instance.is_org_admin,
      'huifu_id': instance.huifu_id,
      'role_ids': instance.role_ids,
      'sub_user_id': instance.sub_user_id,
      'sub_user_type': instance.sub_user_type,
      'user_id': instance.user_id,
      'name': instance.name,
      'email': instance.email,
      'phone_area_code': instance.phone_area_code,
      'phone': instance.phone,
      'is_active': instance.is_active,
      'is_admin': instance.is_admin,
      'refund_permission': instance.refund_permission,
    };
