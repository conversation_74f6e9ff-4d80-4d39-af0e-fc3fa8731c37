// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'main_login_operator_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MainLoginOperatorInfo _$MainLoginOperatorInfoFromJson(
        Map<String, dynamic> json) =>
    MainLoginOperatorInfo(
      uap_sub_user_id: json['uap_sub_user_id'] as String?,
      uap_user_id: json['uap_user_id'] as String?,
      operator_role: json['operator_role'] as String?,
      user_name: json['user_name'] as String?,
      email: json['email'] as String?,
      phone_area_code: json['phone_area_code'] as String?,
      phone: json['phone'] as String?,
      set_pwd_flag: json['set_pwd_flag'] as String?,
      set_trade_pwd_flag: json['set_trade_pwd_flag'] as String?,
      org_cust_id: json['org_cust_id'] as String?,
      full_name: json['full_name'] as String?,
      short_name: json['short_name'] as String?,
      menu_lists: (json['menu_lists'] as List<dynamic>?)
          ?.map((e) => MenuListInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      menu_path_list: (json['menu_path_list'] as List<dynamic>?)
          ?.map((e) => MenuPathInfoVO.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MainLoginOperatorInfoToJson(
        MainLoginOperatorInfo instance) =>
    <String, dynamic>{
      'uap_sub_user_id': instance.uap_sub_user_id,
      'uap_user_id': instance.uap_user_id,
      'operator_role': instance.operator_role,
      'user_name': instance.user_name,
      'email': instance.email,
      'phone_area_code': instance.phone_area_code,
      'phone': instance.phone,
      'set_pwd_flag': instance.set_pwd_flag,
      'set_trade_pwd_flag': instance.set_trade_pwd_flag,
      'org_cust_id': instance.org_cust_id,
      'full_name': instance.full_name,
      'short_name': instance.short_name,
      'menu_lists': instance.menu_lists,
      'menu_path_list': instance.menu_path_list,
    };

MenuPathInfoVO _$MenuPathInfoVOFromJson(Map<String, dynamic> json) =>
    MenuPathInfoVO(
      path: json['path'] as String?,
      name: json['name'] as String?,
      hidden: json['hidden'] as bool?,
      pathType: json['pathType'] as String?,
      componentPath: json['componentPath'] as String?,
      level: json['level'] as String?,
      outResId: json['outResId'] as String?,
      parentOutResId: json['parentOutResId'] as String?,
      meta: json['meta'] == null
          ? null
          : MenuMetaVO.fromJson(json['meta'] as Map<String, dynamic>),
      children: (json['children'] as List<dynamic>?)
          ?.map((e) => MenuPathInfoVO.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MenuPathInfoVOToJson(MenuPathInfoVO instance) =>
    <String, dynamic>{
      'path': instance.path,
      'name': instance.name,
      'hidden': instance.hidden,
      'pathType': instance.pathType,
      'componentPath': instance.componentPath,
      'level': instance.level,
      'outResId': instance.outResId,
      'parentOutResId': instance.parentOutResId,
      'meta': instance.meta,
      'children': instance.children,
    };

MenuMetaVO _$MenuMetaVOFromJson(Map<String, dynamic> json) => MenuMetaVO(
      icon: json['icon'] as String?,
      title: json['title'] as String?,
      permission: json['permission'] as String?,
      keepAlive: json['keepAlive'] as bool?,
      activeMenu: json['activeMenu'] as String?,
      fstMenuRoute: json['fstMenuRoute'] as String?,
      group: json['group'] as String?,
      groupPermission: json['groupPermission'] as String?,
    );

Map<String, dynamic> _$MenuMetaVOToJson(MenuMetaVO instance) =>
    <String, dynamic>{
      'icon': instance.icon,
      'title': instance.title,
      'permission': instance.permission,
      'keepAlive': instance.keepAlive,
      'activeMenu': instance.activeMenu,
      'fstMenuRoute': instance.fstMenuRoute,
      'group': instance.group,
      'groupPermission': instance.groupPermission,
    };

MenuListInfo _$MenuListInfoFromJson(Map<String, dynamic> json) => MenuListInfo(
      belong_sys_id: json['belong_sys_id'] as String?,
      out_res_id: json['out_res_id'] as String?,
      parent_id: json['parent_id'] as String?,
      parent_ids: json['parent_ids'] as String?,
      name: json['name'] as String?,
      sort: (json['sort'] as num?)?.toInt(),
      href: json['href'] as String?,
      icon: json['icon'] as String?,
      level: json['level'] as String?,
      kind: json['kind'] as String?,
      permission: json['permission'] as String?,
      remark: json['remark'] as String?,
      sub: (json['sub'] as List<dynamic>?)
          ?.map((e) => MenuListInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MenuListInfoToJson(MenuListInfo instance) =>
    <String, dynamic>{
      'belong_sys_id': instance.belong_sys_id,
      'out_res_id': instance.out_res_id,
      'parent_id': instance.parent_id,
      'parent_ids': instance.parent_ids,
      'name': instance.name,
      'sort': instance.sort,
      'href': instance.href,
      'icon': instance.icon,
      'level': instance.level,
      'kind': instance.kind,
      'permission': instance.permission,
      'remark': instance.remark,
      'sub': instance.sub,
    };
