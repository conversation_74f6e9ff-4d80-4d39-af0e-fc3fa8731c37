/*
 * ProjectName：UaApp
 * FilePath：lib/pages/main/models
 * FileName：main_ext_user_detail_info
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/26 15:55:20
 */


import 'package:json_annotation/json_annotation.dart';

part 'main_ext_user_detail_info.g.dart';

@JsonSerializable()
class MainExtUserDetailInfo {

  final String? id;
  final String? login_name;
  final String? user_type;
  final String? phone_mask;
  final String? ent_org_admin;
  final String? is_org_admin;
  final String? huifu_id;
  final String? role_ids;

  /// 子用户编号
  final String? sub_user_id;

  /// 子用户类型(1-管理员 2-操作员)
  final String? sub_user_type;

  /// 用户编号
  final String? user_id;

  /// 用户名称
  final String? name;

  /// 邮箱
  final String? email;

  /// 区号
  final String? phone_area_code;

  /// 手机号
  final String? phone;

  /// 用户状态(枚举：Y-启用 N-禁用)
  final String? is_active;

  /// 是否管理员(枚举：Y-是 N-否)
  final String? is_admin;

  /// 退款权限(枚举：Y-开启 N-关闭)
  final String? refund_permission;


  MainExtUserDetailInfo({
    this.id,
    this.login_name,
    this.user_type,
    this.phone_mask,
    this.ent_org_admin,
    this.is_org_admin,
    this.huifu_id,
    this.role_ids,
    this.sub_user_id,
    this.sub_user_type,
    this.user_id,
    this.name,
    this.email,
    this.phone_area_code,
    this.phone,
    this.is_active,
    this.is_admin,
    this.refund_permission,
  });

  factory MainExtUserDetailInfo.fromJson(Map<String, dynamic> json) => _$MainExtUserDetailInfoFromJson(json);
  Map<String, dynamic> toJson() => _$MainExtUserDetailInfoToJson(this);
}
