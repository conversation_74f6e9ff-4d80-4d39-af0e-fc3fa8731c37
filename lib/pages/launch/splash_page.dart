import 'dart:async';

import 'package:auapp/pub/constants/storage_constants.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../r.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  Timer? _timer;
  int _countdown = 3;

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) return;
      if (_countdown <= 1) {
        timer.cancel();
        _navigateToLogin();
      } else {
        setState(() {
          _countdown--;
        });
      }
    });
  }

  void _navigateToLogin() {
    if (!mounted) return;
    _timer?.cancel();
    var storage = Get.find<SharedPreferences>();
    try {
      if (storage.getBool(StorageConstants.loginFlag) ?? false) {
        HPRouter.resolvePage(Routes.main);
      } else {
        HPRouter.resolvePage(Routes.login);
      }
    } catch (e) {
      HPRouter.resolvePage(Routes.login);
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.only(top: 44.sp),
        child: Stack(
          fit: StackFit.expand,
          children: [
            Image.asset(
              R.assetsImagesAppLauncher,
              fit: BoxFit.fill,
            ),
            Positioned(
              top: 20.sp,
              right: 20.sp,
              child: GestureDetector(
                onTap: () {
                  _navigateToLogin();
                },
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.sp,
                    vertical: 6.sp,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(15.sp),
                  ),
                  child: Text(
                    '${'splash_skip'.tr} $_countdown',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14.sp,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
