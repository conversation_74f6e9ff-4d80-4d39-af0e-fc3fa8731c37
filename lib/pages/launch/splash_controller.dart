import 'package:auapp/pub/constants/storage_constants.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/services/storage_service.dart';
import 'package:auapp/utils/hp_localization.dart';
import 'package:get/get.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SplashController extends GetxController {

  @override
  void onReady() async {
    super.onReady();
    /// 初始语言国际化配置
    String language =
        StorageService.getString(StorageConstants.huepayLanguage) ?? "en-US";
    HPLocalization.localize(language);

    await Future.delayed(const Duration(milliseconds: 2000));
    var storage = Get.find<SharedPreferences>();
    try {
      if (storage.getBool(StorageConstants.loginFlag) ?? false) {
        HPRouter.resolvePage(Routes.main);
      } else {
        HPRouter.resolvePage(Routes.login);
      }
    } catch (e) {
      HPRouter.resolvePage(Routes.login);
    }
  }
}
