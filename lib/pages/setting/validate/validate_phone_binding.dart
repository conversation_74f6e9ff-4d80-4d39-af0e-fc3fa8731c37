/*
 * ProjectName：UaApp
 * FilePath：lib/pages/setting/validate
 * FileName：validate_phone_binding
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/28 15:05:31
 */

import 'package:auapp/base/base_binding.dart';
import 'package:auapp/pages/setting/validate/validate_phone_controller.dart';
import 'package:auapp/pages/setting/validate/validate_phone_provider.dart';
import 'package:get/get.dart';

class ValidatePhoneBinding extends BaseBinding {
  @override
  void dependencies() {
    Get.lazyPut(() => ValidatePhoneController(validatePhoneProvider: Get.find()));
    Get.lazyPut(() => ValidatePhoneProvider());
  }
}