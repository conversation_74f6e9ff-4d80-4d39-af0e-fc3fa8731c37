/*
 * ProjectName：UaApp
 * FilePath：lib/pages/setting/validate
 * FileName：validate_phone_controller
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/28 15:05:20
 */

import 'dart:async';

import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/models/HPUserInfoSingleton.dart';
import 'package:auapp/pages/login/utils/login_validator.dart';
import 'package:auapp/pages/setting/validate/validate_phone_provider.dart';
import 'package:auapp/pub/constants/storage_constants.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/services/storage_service.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class ValidatePhoneController extends GetxController {
  final ValidatePhoneProvider validatePhoneProvider;
  ValidatePhoneController({required this.validatePhoneProvider});

  final validateCodeInputController = TextEditingController();
  final validateCode = "".obs;
  final countAreaCode = "".obs;
  final phoneNum = "".obs;

  final verifyCodeBtnTitle = ''.obs;

  final showDel = false.obs;

  late int seconds = 60; // 倒计时总秒数
  final isCounting = false.obs; // 倒计时状态标识
  Timer? timer; // 定时器对象
  final hasSendCode = false.obs;

  final codeHasInput = false.obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();

    phoneNum.value = HPUserInfoSingleton().phone ?? "";
    countAreaCode.value = HPUserInfoSingleton().phoneAreaCode ?? "";

    hasSendCode.value = false;
    verifyCodeBtnTitle.value = 'login_send_code_title'.tr;

    seconds = 60; // 倒计时总秒数
    isCounting.value = false; // 倒计时状态标识
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
  }

  /// 发送验证码
  Future<void> sendValidateCode() async {
    HPResponse sendVerifyCodeResponse =
        await validatePhoneProvider.sendVerifyCode();
    if (sendVerifyCodeResponse.isSuccess) {
      hasSendCode.value = true;
      _startCountdown();
      HPLoading.showSuccess('login_send_code_success_tips'.tr);
    } else {
      HPLoading.showError(sendVerifyCodeResponse.msg);
    }
  }

  /// 注销账号
  Future<void> closeAccount() async {
    if (!hasSendCode.value) {
      HPLoading.showToast('login_send_code_tips'.tr);
      return;
    }
    if (LoginValidator.validateCode(validateCodeInputController.text)) {
      HPResponse response = await validatePhoneProvider
          .closeAccount(validateCodeInputController.text);
      if (response.isSuccess) {
        StorageService.setBool(StorageConstants.loginFlag, false);
        StorageService.remove(StorageConstants.token);
        StorageService.remove(StorageConstants.userInfo);
        StorageService.remove(StorageConstants.loginOperatorInfo);
        StorageService.remove(StorageConstants.loginOperatorExtInfo);
        HPUserInfoSingleton().clearUserInfo();
        HPRouter.pushPage(Routes.closeAccountResult);
      } else {
        HPLoading.showError(response.msg);
      }
    }
  }

  void _startCountdown() {
    print('开始倒计时...');

    isCounting.value = true;
    seconds = 60;

    // 设置周期性定时器（每秒执行一次）
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (seconds > 0) {
        print('倒计时剩余时间：${seconds}s');
        verifyCodeBtnTitle.value =
            '${'login_resend_code_title'.tr} ${seconds}s';
        seconds--; // 秒数递减
      } else {
        timer.cancel(); // 倒计时结束取消定时器
        isCounting.value = false; // 重置状态
        verifyCodeBtnTitle.value = 'login_resend_code_title'.tr;
      }
    });
  }

  @override
  void onClose() {
    timer?.cancel(); // 倒计时结束取消定时器
    isCounting.value = false; // 重置状态
    super.onClose();
  }
}
