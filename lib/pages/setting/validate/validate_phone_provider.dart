/*
 * ProjectName：UaApp
 * FilePath：lib/pages/setting/validate
 * FileName：validate_phone_provider
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/28 15:05:43
 */

import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';

class ValidatePhoneProvider {
  /// 获取验证码接口
  Future<HPResponse> sendVerifyCode() async {
    Map<String, dynamic> data = {
      "type": "U_STATE",
    };
    HPResponse response = await HttpRequest.post(
        ApiConstants.sendSafetyVerifyCodeUrl,
        params: data);
    return response;
  }

  /// 注销账号接口
  Future<HPResponse> closeAccount(String smsCode) async {
    Map<String, dynamic> data = {"sms_code": smsCode};
    HPResponse response =
        await HttpRequest.post(ApiConstants.closeAccountUrl, params: data);
    return response;
  }
}
