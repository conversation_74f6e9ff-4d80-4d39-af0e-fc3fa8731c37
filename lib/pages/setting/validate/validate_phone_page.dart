/*
 * ProjectName：UaApp
 * FilePath：lib/pages/setting/validate
 * FileName：validate_phone_page
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/28 15:05:06
 */

import 'package:auapp/pages/setting/validate/validate_phone_controller.dart';
import 'package:auapp/pub/constants/color_constants.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/utils/app_focus.dart';
import 'package:auapp/utils/hp_input_formater.dart';
import 'package:auapp/widgets/hp_app_bar.dart';
import 'package:auapp/widgets/hp_border_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

import '../../../r.dart';

class ValidatePhonePage extends GetView<ValidatePhoneController> {
  const ValidatePhonePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: HPAppBar(
        text: 'setting_security_validate_title'.tr,
        backgroundColor: Colors.white,
      ),
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false, // 默认值为 true，自动调整布局
      body: GestureDetector(
        onTap: () {
          AppFocus.unfocus(context);
        },
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(top: 8.h, left: 16.w, right: 16.w),
              padding: EdgeInsets.symmetric(vertical: 6.w, horizontal: 16.w),
              decoration: BoxDecoration(
                color: const Color(0xFFEBF9FE),
                borderRadius: BorderRadius.all(Radius.circular(9.w)),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 3.w),
                    child: Image.asset(
                      R.assetsImagesSettingAlertIcon,
                      width: 14.w,
                      height: 14.w,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Container(
                      alignment: Alignment.topLeft,
                      child: Text(
                        'setting_security_validate_phone_alert_tips'.tr,
                        softWrap: true, // 启用换行
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: ThemeConfig.mainThemeColor,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
              alignment: Alignment.centerLeft,
              child: Obx(
                () => Text(
                  '${"setting_security_validate_phone_send_to_tips".tr} +${controller.countAreaCode.value} ${controller.phoneNum.value}',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: const Color(0xFF6B7275),
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 2.h, left: 16.w, right: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                              top: 15.h,
                              bottom: 15.h,
                            ),
                            child: Text(
                              'setting_security_validate_phone_code_title'.tr,
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: ThemeConfig.darkTextColor,
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 120.sp,
                            child: CupertinoTextField(
                              padding: EdgeInsets.only(
                                  top: 10.h, left: 16.w, bottom: 10.h),
                              controller:
                                  controller.validateCodeInputController,
                              maxLines: 1,
                              maxLength: 6,
                              enabled: true,
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                HPInputFormatter.inputNumberFormatter
                              ],
                              style: TextStyle(
                                color: ThemeConfig.darkTextColor,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.normal,
                              ),
                              textAlign: TextAlign.left,
                              onChanged: (text) {
                                controller.showDel.value = text.isNotEmpty;
                                controller.codeHasInput.value = text.isNotEmpty;
                              },
                              placeholder: 'login_verify_code_placeholder'.tr,
                              placeholderStyle: TextStyle(
                                fontSize: 16.sp,
                                color: const Color(0xFFC9CDD4),
                                fontWeight: FontWeight.normal,
                              ),
                              decoration: null,
                              autocorrect: false,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Obx(
                            () => Visibility(
                              visible: controller.showDel.value,
                              child: GestureDetector(
                                onTap: () {
                                  controller.validateCodeInputController
                                      .clear();
                                  controller.showDel.value = false;
                                  controller.codeHasInput.value = false;
                                },
                                child: Image.asset(
                                  R.assetsImagesLoginIconDel,
                                  width: 24.sp,
                                  height: 24.sp,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 8.sp),
                          GestureDetector(
                            onTap: () {
                              if (controller.isCounting.value) {
                                return;
                              }
                              controller.sendValidateCode();
                            },
                            child: Obx(
                              () => Text(
                                controller.verifyCodeBtnTitle.value,
                                style: TextStyle(
                                    fontSize: 16.sp,
                                    color: controller.isCounting.value
                                        ? const Color(0xFF9DE2FB)
                                        : ColorConstants.mainThemeColor),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  Divider(
                    color: const Color(0xFFE5E6EB),
                    height: 0.5.sp,
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 70.h),
              child: Obx(
                () => HPBorderButton(
                  width: 1.sw - 32.w,
                  text: "common_btn_next".tr,
                  backgroundColor: (controller.hasSendCode.value &&
                          controller.codeHasInput.value)
                      ? ColorConstants.mainThemeColor
                      : ColorConstants.mainDisableThemeColor,
                  onPressed: () {
                    if (controller.hasSendCode.value &&
                        controller.codeHasInput.value) {
                      /// 注销账户
                      controller.closeAccount();
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
