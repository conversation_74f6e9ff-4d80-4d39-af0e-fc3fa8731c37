import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/base/base_controller.dart';
import 'package:auapp/models/HPUserInfoSingleton.dart';
import 'package:auapp/pub/constants/storage_constants.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/services/storage_service.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:auapp/widgets/hp_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class SettingController extends BaseController {
  SettingController();

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }

  /// 退出登录
  Future<void> logout(BuildContext context) async {
    HPDialog.show(
      context,
      title: 'setting_sign_out_dialog_title'.tr,
      content: 'setting_sign_out_dialog_content'.tr,
      confirmText: 'setting_sign_out_dialog_confirm'.tr,
      onConfirmPress: () async {
        await signOut();
      },
    );
  }

  /// 注销账号
  void closeAccount(BuildContext context) {
    HPDialog.show(
      context,
      title: 'setting_account_cancel_btn_title'.tr,
      content: 'setting_close_account_dialog_content'.tr,
      confirmText: 'setting_close_account_dialog_confirm'.tr,
      cancelText: 'setting_close_account_dialog_cancel'.tr,
      onCancelPress: () async {
        /// 确认
        HPRouter.pushPage(Routes.closeAccount);
      },
    );
  }

  Future<void> signOut() async {
    try {
      HPResponse response = await HttpRequest.get(ApiConstants.logoutUrl);
      if (response.isSuccess) {
        StorageService.setBool(StorageConstants.loginFlag, false);
        StorageService.remove(StorageConstants.token);
        StorageService.remove(StorageConstants.userInfo);
        StorageService.remove(StorageConstants.loginOperatorInfo);
        StorageService.remove(StorageConstants.loginOperatorExtInfo);
        HPUserInfoSingleton().clearUserInfo();
        HPRouter.resolvePage(Routes.login);
      } else {
        HPLoading.showError(response.msg);
      }
    } catch (e) {
      HPLoading.showToast(e.toString());
    }
  }
}
