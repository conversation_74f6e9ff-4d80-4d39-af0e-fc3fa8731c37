/*
 * ProjectName：UaApp
 * FilePath：lib/pages/setting/closeAccount
 * FileName：close_account_page
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/28 13:13:36
 */

import 'package:auapp/pages/setting/closeAccount/close_account_controller.dart';
import 'package:auapp/pub/constants/color_constants.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/widgets/hp_app_bar.dart';
import 'package:auapp/widgets/hp_border_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../r.dart';

class CloseAccountPage extends GetView<CloseAccountController> {
  const CloseAccountPage({super.key});
  @override
  Widget build(BuildContext context) {
    // 获取 AppBar 的默认高度
    final appBarHeight = AppBar().preferredSize.height;
    // 获取状态栏高度（如果需要适配全屏）
    final statusBarHeight = MediaQuery.of(context).padding.top;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.white,
      appBar: HPAppBar(
        text: 'setting_account_cancel_btn_title'.tr,
      ),
      body: Stack(
        children: [
          Positioned(
            left: 0,
            top: 0,
            width: 1.sw,
            height: 192.h,
            child: Image.asset(
              R.assetsImagesSettingCloseAccountTopBg,
              fit: BoxFit.cover,
            ),
          ),
          Positioned.fill(
            top: (appBarHeight + statusBarHeight),
            child: Column(
              // shrinkWrap: true,
              // physics: const ClampingScrollPhysics(),
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 32.sp),
                  child: Image.asset(
                    width: 61.w,
                    height: 61.w,
                    R.assetsImagesSettingCloseAccountAlertIcon,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 12.sp, left: 16.w, right: 16.w),
                  child: Text(
                    'setting_account_cancel_tips'.tr,
                    style: TextStyle(
                        fontSize: 18.sp,
                        color: ColorConstants.darkTextColor,
                        fontWeight: FontWeight.w500),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 32.sp, left: 16.w, right: 16.w),
                  child: Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF9F9F9),
                      borderRadius: BorderRadius.circular(14.sp),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: 21.w,
                              alignment: Alignment.topLeft,
                              child: Text(
                                '1.',
                                softWrap: true, // 启用换行
                                style: TextStyle(
                                  fontSize: 13.0.sp,
                                  color: const Color(0xFF6B7275),
                                ),
                              ),
                            ),
                            Expanded(
                              child: Container(
                                alignment: Alignment.topRight,
                                child: Text(
                                  "setting_account_cancel_tips_1".tr,
                                  softWrap: true, // 启用换行
                                  style: TextStyle(
                                    fontSize: 13.0.sp,
                                    color: ColorConstants.darkTextColor,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 17.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: 21.w,
                              alignment: Alignment.topLeft,
                              child: Text(
                                '2.',
                                softWrap: true, // 启用换行
                                style: TextStyle(
                                  fontSize: 13.0.sp,
                                  color: const Color(0xFF6B7275),
                                ),
                              ),
                            ),
                            Expanded(
                              child: Container(
                                alignment: Alignment.topRight,
                                child: Text(
                                  "setting_account_cancel_tips_2".tr,
                                  softWrap: true, // 启用换行
                                  style: TextStyle(
                                    fontSize: 13.0.sp,
                                    color: ColorConstants.darkTextColor,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 70.sp),
                  child: HPBorderButton(
                    width: 1.sw - 48.w,
                    text: "setting_account_cancel_back".tr,
                    backgroundColor: const Color(0xFFF5F5F5),
                    textStyle: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFF464B4E),
                      fontWeight: FontWeight.w500,
                    ),
                    onPressed: () {
                      HPRouter.popBack();
                    },
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 16.sp),
                  child: HPBorderButton(
                    width: 1.sw - 48.w,
                    text: "setting_account_cancel_confirm".tr,
                    onPressed: () {
                      HPRouter.pushPage(Routes.securityValidate);
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
