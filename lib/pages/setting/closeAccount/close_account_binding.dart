/*
 * ProjectName：UaApp
 * FilePath：lib/pages/setting/closeAccount
 * FileName：close_account_binding
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/28 13:25:31
 */

import 'package:auapp/base/base_binding.dart';
import 'package:auapp/pages/setting/closeAccount/close_account_controller.dart';
import 'package:get/get.dart';

class CloseAccountBinding implements BaseBinding {
  @override
  void dependencies() {
    Get.lazyPut<CloseAccountController>(() => CloseAccountController());
  }
}
