/*
 * ProjectName：UaApp
 * FilePath：lib/pages/setting/closeAccount
 * FileName：close_account_provider
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/28 13:25:43
 */

import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';

class CloseAccountProvider {
  /// 交易详情接口
  Future<HPResponse> fetchTransactionDetail(String transactionId) async {
    HPResponse response = await HttpRequest.post(
        ApiConstants.closeAccountUrl,
        params: {
          "transactionId": transactionId,
        }
    );
    return response;
  }
}
