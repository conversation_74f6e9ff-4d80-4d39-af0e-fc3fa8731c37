/*
 * ProjectName：UaApp
 * FilePath：lib/pages/setting/closeAccount
 * FileName：close_account_status_controller
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/29 10:57:11
 */

import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:get/get.dart';

class CloseAccountStatusController extends GetxController {
  CloseAccountStatusController();

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
  }

  void toLoginPage() {
    HPRouter.resolvePage(Routes.login);
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
  }
}
