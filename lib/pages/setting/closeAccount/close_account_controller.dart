/*
 * ProjectName：UaApp
 * FilePath：lib/pages/setting/closeAccount
 * FileName：close_account_controller
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/28 13:13:50
 */


import 'package:auapp/widgets/hp_dialog.dart';
import 'package:flutter/cupertino.dart';

import 'close_account_provider.dart';
import 'package:get/get.dart';

class CloseAccountController extends GetxController {
  final CloseAccountProvider closeAccountProvider;
  CloseAccountController({required this.closeAccountProvider});

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
  }

  /// 注销账号
  Future<void> closeAccount(BuildContext context) async {

  }

  @override
  void onClose() {
    super.onClose();
  }
}
