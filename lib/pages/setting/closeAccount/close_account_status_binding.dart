/*
 * ProjectName：UaApp
 * FilePath：lib/pages/setting/closeAccount
 * FileName：close_account_status_binding
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/29 10:50:25
 */

import 'package:auapp/base/base_binding.dart';
import 'package:auapp/pages/setting/closeAccount/close_account_status_controller.dart';
import 'package:get/get.dart';

class CloseAccountStatusBinding extends BaseBinding {
  @override
  void dependencies() {
    Get.lazyPut(() => CloseAccountStatusController());
  }
}
