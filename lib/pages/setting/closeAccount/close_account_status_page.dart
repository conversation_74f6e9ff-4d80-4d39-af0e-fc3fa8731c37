/*
 * ProjectName：UaApp
 * FilePath：lib/pages/setting/closeAccount
 * FileName：close_account_status_page
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/29 10:45:59
 */

import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/widgets/hp_app_bar.dart';
import 'package:auapp/widgets/hp_border_button.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:auapp/pages/setting/closeAccount/close_account_status_controller.dart';
import 'package:flutter/material.dart';

import '../../../r.dart';

class CloseAccountStatusPage extends GetView<CloseAccountStatusController> {
  const CloseAccountStatusPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: HPAppBar(
        text: '',
        backgroundColor: Colors.white,
        backFunc: () {
          controller.toLoginPage();
        },
      ),
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false, // 默认值为 true，自动调整布局
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            margin: EdgeInsets.only(top: 88.h),
            child: Image.asset(
              R.assetsImagesSettingCloseAccountSuccess,
              width: 88.w,
              height: 88.w,
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 12.h, left: 24.w, right: 24.w),
            child: Text(
              'setting_security_close_account_success_message'.tr,
              softWrap: true, // 启用换行
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: ThemeConfig.darkTextColor,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 48.h),
            child: HPBorderButton(
              width: 1.sw - 48.w,
              text: "common_btn_done".tr,
              onPressed: () {
                /// 注销账户
                controller.toLoginPage();
              },
            ),
          ),
        ],
      ),
    );
  }
}
