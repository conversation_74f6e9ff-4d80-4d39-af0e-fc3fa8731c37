import 'package:auapp/pages/setting/setting_controller.dart';
import 'package:auapp/pub/constants/storage_constants.dart';
import 'package:auapp/r.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/services/app_info_service.dart';
import 'package:auapp/services/storage_service.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/widgets/hp_app_bar.dart';
import 'package:auapp/widgets/hp_border_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SettingPage extends GetView<SettingController> {
  SettingPage({super.key});

  final languageList = [
    {
      "region": '简体中文',
      "language": 'zh-CN',
      "icon": R.assetsImagesLoginRegionChina,
    },
    {
      "region": 'English',
      "language": 'en-US',
      "icon": R.assetsImagesLoginRegionAustralia,
    },
  ];

  Widget _returnCell(String title, String path, {String? text}) {
    String imgResource = languageList.firstWhere((element) =>
            element['language'] ==
            StorageService.getString(
                StorageConstants.huepayLanguage))['icon'] ??
        '';
    return GestureDetector(
      onTap: () {
        if (path.isEmpty) return;
        HPRouter.pushPage(path);
      },
      child: Container(
        color: Colors.white,
        padding:
            EdgeInsets.only(top: 15.h, bottom: 0.h, left: 16.w, right: 0.w),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: ThemeConfig.norBackText12.copyWith(fontSize: 16.sp),
                ),
                Row(
                  children: [
                    Visibility(
                      visible: path == Routes.settingLanguage &&
                          imgResource.isNotEmpty,
                      child: Image.asset(
                        imgResource,
                        width: 24.sp,
                        height: 24.sp,
                      ),
                    ),
                    Visibility(
                      visible: (text ?? "").isNotEmpty,
                      child: Text(
                        text ?? "",
                        style:
                            ThemeConfig.norBackText12.copyWith(fontSize: 13.sp),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(right: 16.w, left: 8.w),
                      child: const Icon(
                        Icons.arrow_forward_ios,
                        size: 16.0,
                        color: ThemeConfig.norBackColor,
                      ),
                    ),
                  ],
                )
              ],
            ),
            SizedBox(height: 15.h),
            Divider(
              height: 1.h,
              color: const Color(0xFFF0F0F0),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 获取当前APP版本
    final appVersion = Get.find<AppInfoService>().appVersion;
    final List<Map<String, String>> funcList = [
      // {
      //   "name": "setting_security".tr,
      //   "path": Routes.settingLanguage,
      // },
      // {
      //   "name": "setting_voice".tr,
      //   "path": Routes.settingLanguage,
      // },
      // {
      //   "name": "setting_customer_bear_fee".tr,
      //   "path": Routes.settingLanguage,
      // },
      {
        "name": "setting_language".tr,
        "path": Routes.settingLanguage,
        "text": "",
      },
      // {
      //   "name": "setting_feedback".tr,
      //   "path": Routes.settingLanguage,
      // },
      // {
      //   "name": "setting_about_us".tr,
      //   "path": Routes.settingLanguage,
      // },
      {
        "name": "setting_version".tr,
        "path": "",
        "text": "V${appVersion}",
      },
    ];
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
    );
    return Scaffold(
      appBar: HPAppBar(
        text: "mine_settings".tr,
      ),
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.only(top: 0.h),
            width: 1.sw,
            height: 1.sh - 260.h,
            child: ListView(
              padding: const EdgeInsets.all(0),
              shrinkWrap: false,
              children: [
                ...funcList.map((e) =>
                    _returnCell(e["name"]!, e["path"]!, text: e["text"])),
              ],
            ),
          ),
          HPBorderButton(
            text: "login_out_title".tr,
            backgroundColor: const Color(0xFFF5F5F5),
            textColor: const Color(0xFF464B4E),
            width: 1.sw - 48.w,
            onPressed: () {
              controller.logout(context);
            },
          ),
          SizedBox(height: 16.h),
          HPBorderButton(
            text: "setting_account_cancel_btn_title".tr,
            backgroundColor: Colors.white,
            textStyle:
                TextStyle(fontSize: 13.sp, color: const Color(0xFF6B7275)),
            width: 1.sw - 48.w,
            onPressed: () {
              controller.closeAccount(context);
            },
          ),
        ],
      ),
    );
  }
}
