/*
 * ProjectName：UaApp
 * FilePath：lib/pages/setting/language
 * FileName：language_setting_controller
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/28 10:39:12
 */
import 'package:auapp/pub/constants/storage_constants.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/services/storage_service.dart';
import 'package:auapp/utils/hp_localization.dart';
import 'package:get/get.dart';

import '../../../r.dart';

class LanguageSettingController extends GetxController {
  LanguageSettingController();

  final languageList = [
    {
      "region": '简体中文',
      "language": 'zh-CN',
      "icon": R.assetsImagesLoginRegionChina,
    },
    {
      "region": 'English',
      "language": 'en-US',
      "icon": R.assetsImagesLoginRegionAustralia,
    },
  ].obs;

  late final currentLanguage = "".obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();

    currentLanguage.value = StorageService.getString(StorageConstants.huepayLanguage) ?? "en-US";
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
  }

  void setLanguage() {
    HPLocalization.localize(currentLanguage.value);
    HPRouter.popBack();
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
  }
}
