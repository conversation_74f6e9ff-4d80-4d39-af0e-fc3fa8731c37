/*
 * ProjectName：UaApp
 * FilePath：lib/pages/setting/language
 * FileName：language_setting_page
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/28 10:38:55
 */
import 'package:auapp/pages/setting/language/language_setting_controller.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/utils/app_focus.dart';
import 'package:auapp/widgets/hp_app_bar.dart';
import 'package:auapp/widgets/hp_border_button.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

import '../../../r.dart';

class LanguageSettingPage extends GetView<LanguageSettingController> {
  const LanguageSettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: HPAppBar(
        text: 'setting_language_page_title'.tr,
        backgroundColor: Colors.white,
      ),
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false, // 默认值为 true，自动调整布局
      body: GestureDetector(
        onTap: () {
          AppFocus.unfocus(context);
        },
        child: Column(
          children: [
            Expanded(
              child: ListView.builder(
                itemCount: controller.languageList.length,
                itemBuilder: (context, index) => GestureDetector(
                  onTap: () {
                    controller.currentLanguage.value =
                        controller.languageList[index]['language'] ?? "en-US";
                  },
                  child: Column(
                    children: [
                      Container(
                        color: Colors.white,
                        padding: EdgeInsets.symmetric(
                          vertical: 8.sp,
                          horizontal: 16.sp,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Container(
                                  width: 38.sp,
                                  height: 38.sp,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.all(
                                        Radius.circular(19.sp)),
                                    color: const Color(0xFFF5F5F5),
                                  ),
                                  child: Image.asset(
                                    controller.languageList[index]['icon'] ??
                                        '',
                                    width: 24.sp,
                                    height: 24.sp,
                                  ),
                                ),
                                SizedBox(width: 12.sp),
                                Text(
                                  controller.languageList[index]['region'] ??
                                      '',
                                  style: TextStyle(
                                      color: ThemeConfig.darkTextColor,
                                      fontSize: 14.sp),
                                )
                              ],
                            ),
                            Obx(
                              () => Visibility(
                                visible: controller.languageList[index]
                                        ['language'] ==
                                    controller.currentLanguage.value,
                                child: Image.asset(
                                  R.assetsImagesLoginRegionChoose,
                                  width: 24.sp,
                                  height: 24.sp,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        height: 0.5.sp,
                        color: const Color(0xFFE5E6EB),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            _buildBottom(context),
          ],
        ),
      ),
    );
  }

  Widget _buildBottom(BuildContext context) {
    return Container(
      color: Colors.white,
      child: SafeArea(
        // 处理安全区域（如刘海屏）
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 24.sp, vertical: 12.sp),
          alignment: Alignment.center,
          child: SizedBox(
            width: 1.sw,
            child: HPBorderButton(
              text: 'common_btn_save'.tr,
              onPressed: () {
                controller.setLanguage();
              },
            ),
          ),
        ),
      ),
    );
  }
}
