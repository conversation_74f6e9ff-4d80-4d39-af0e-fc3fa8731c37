/*
 * ProjectName：UaApp
 * FilePath：lib/pages/webView
 * FileName：web_view_controller
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/16 18:52:37
 */

import 'package:auapp/base/base_controller.dart';
import 'package:auapp/pages/webView/web_view_provider.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

class HPWebViewController extends BaseController {
  final HPWebViewProvider webViewProvider;
  HPWebViewController({required this.webViewProvider});

  late final String url;  // 网页 URL
  late final String title;  // 网页 title
  late WebViewController webController;  // WebView 控制器
  var isLoading = true.obs;  // 加载状态

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    url = Get.parameters['url']!;  // 从路由参数获取 URL
    title = Get.parameters['title']!;
    _initWebView();
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
  }

  void _initWebView() {
    webController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)  // 启用 JavaScript
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            isLoading.value = progress < 100;  // 更新加载状态
          },
          onPageStarted: (String url) => isLoading.value = true,
          onPageFinished: (String url) => isLoading.value = false,
        ),
      )
      ..loadRequest(Uri.parse(url));  // 加载 URL
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
  }
}
