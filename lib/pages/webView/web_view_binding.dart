/*
 * ProjectName：UaApp
 * FilePath：lib/pages/webView
 * FileName：web_view_binding
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/16 18:52:25
 */

import 'package:auapp/base/base_binding.dart';
import 'package:auapp/pages/webView/web_view_controller.dart';
import 'package:auapp/pages/webView/web_view_provider.dart';
import 'package:get/get.dart';

class HPWebViewBinding implements BaseBinding {
  @override
  void dependencies() {
    Get.lazyPut<HPWebViewController>(() => HPWebViewController(webViewProvider: Get.find()));
    Get.lazyPut(()=>HPWebViewProvider());
  }
}
