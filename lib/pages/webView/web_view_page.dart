/*
 * ProjectName：UaApp
 * FilePath：lib/pages/webView
 * FileName：web_view_page
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/16 18:52:10
 */
import 'package:auapp/pages/webView/web_view_controller.dart';
import 'package:auapp/widgets/hp_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';


class HPWebViewPage extends GetView<HPWebViewController> {
  const HPWebViewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: HPAppBar(
        text: controller.title,
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: controller.webController),  // 核心 WebView
          Obx(() => controller.isLoading.value
              ? const Center(child: CircularProgressIndicator())
              : const SizedBox.shrink()),  // 加载指示器
        ],
      ),
    );
  }
}
