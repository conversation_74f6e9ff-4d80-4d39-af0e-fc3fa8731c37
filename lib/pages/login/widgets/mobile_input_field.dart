/*
 * ProjectName：UaApp
 * FilePath：lib/pages/login/widgets
 * FileName：mobile_input_field
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/18 16:06:29
 */
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../pub/constants/color_constants.dart';
import '../../../r.dart';

class MobileInputField extends StatefulWidget {
  final TextEditingController controller;
  final TextInputType keyboardType;
  final String labelText;
  final String placeholder;
  final Color color;
  final double fontSize;
  final String? initialRegionCode;
  final int? maxLength; // 最大长度
  final List<TextInputFormatter>? inputFormatters;
  final Function(String code)? onRegionChanged;
  final Function(String text)? onEditChanged;


  const MobileInputField({
    super.key,
    required this.controller,
    this.keyboardType = TextInputType.text,
    this.labelText = '',
    this.placeholder = '',
    this.color = ColorConstants.darkTextColor,
    this.fontSize = 16.0,
    this.initialRegionCode = "61",
    this.maxLength = 15,
    this.inputFormatters,
    this.onRegionChanged,
    this.onEditChanged,
  });

  @override
  State<MobileInputField> createState() => _MobileInputFieldState();
}

class _MobileInputFieldState extends State<MobileInputField> {
  bool showDel = false;
  bool showErrorTips = false;
  late String regionCode;
  late String sectionCode;
  late int mobileMaxLength;
  final Map<String, GlobalKey> _headerKeys = {};
  late var regionList = [];

  @override
  void initState() {
    super.initState();
    regionCode = widget.initialRegionCode ?? "61";
    mobileMaxLength = widget.maxLength ?? 15;
    if (regionCode == "86") {
      mobileMaxLength = 11;
    }
    else {
      mobileMaxLength = 15;
    }
    sectionCode = "";
    // 初始化分组标题的 GlobalKey
    for (final group in regionList) {
      _headerKeys[group["itemKey"] as String] = GlobalKey();
    }
  }

  @override
  Widget build(BuildContext context) {
    showDel = widget.controller.text.isNotEmpty;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        /// titleLabel
        Container(
          height: 22.sp,
          alignment: Alignment.centerLeft,
          margin: EdgeInsets.only(bottom: 6.sp),
          child: Text(
            widget.labelText,
            style: TextStyle(
              fontSize: 14.sp,
              color: widget.color,
              fontWeight: FontWeight.normal,
            ),
          ),
        ),

        /// 主体
        SizedBox(
          height: 44.sp,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () {
                  regionList = [
                    {
                      "itemKey": "A",
                      "itemList": [
                        {"country": "login_region_country_australia".tr, "code": "61", "indexKey": "A"}
                      ],
                    },
                    {
                      "itemKey": "C",
                      "itemList": [
                        {"country": "login_region_country_china".tr, "code": "86", "indexKey": "C"},
                        {"country": "login_region_country_hongkong".tr, "code": "852", "indexKey": "C"}
                      ],
                    },
                  ];
                  showModalBottomSheet(
                    context: context,
                    // 设置顶部圆角
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(16.sp)),
                    ),
                    backgroundColor: Colors.white, // 背景颜色
                    isScrollControlled: true, // 允许滚动（内容较长时）
                    builder: (context) => StatefulBuilder(
                      builder:
                          (BuildContext context, StateSetter setModelState) {
                        return Container(
                          height: 400.sp,
                          padding: EdgeInsets.symmetric(vertical: 16.sp),
                          child: Column(
                            children: [
                              // 标题和关闭按钮
                              Container(
                                padding:
                                    EdgeInsets.symmetric(horizontal: 16.sp),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    SizedBox(
                                      width: 24.sp,
                                    ),
                                    Text(
                                      'login_select_region'.tr,
                                      style: TextStyle(
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w500,
                                          color: ColorConstants.darkTextColor),
                                    ),
                                    GestureDetector(
                                      onTap: () => Navigator.pop(context),
                                      child: Image.asset(
                                        R.assetsImagesLoginPopupClose,
                                        width: 24.sp,
                                        height: 24.sp,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                padding:
                                    EdgeInsets.only(left: 16.sp, right: 9.sp),
                                height: 340.sp,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Container(
                                      child: Expanded(
                                        child: _buildScrollListViewItems(
                                            context, regionList, setModelState),
                                      ),
                                    ),
                                    Container(
                                      width: 17.sp,
                                      height: 100.sp,
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          GestureDetector(
                                            onTap: () {
                                              /// 点击 sectionCode 滚动页面
                                              setState(() {
                                                sectionCode = "A";

                                                /// 触发滚动到对应分组
                                                _scrollToSection(sectionCode);
                                              });
                                              setModelState(() {});
                                            },
                                            child: Container(
                                              width: 18.sp,
                                              height: 18.sp,
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                color: sectionCode == "A"
                                                    ? ColorConstants
                                                        .mainThemeColor
                                                    : Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(9.sp),
                                              ),
                                              child: Text(
                                                'A',
                                                style: TextStyle(
                                                    color: sectionCode == "A"
                                                        ? Colors.white
                                                        : ColorConstants
                                                            .darkTextColor,
                                                    fontSize: 11.sp,
                                                    fontWeight:
                                                        FontWeight.w500),
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 8.sp),
                                          GestureDetector(
                                            onTap: () {
                                              /// 点击 sectionCode 滚动页面
                                              setState(() {
                                                sectionCode = "C";

                                                /// 触发滚动到对应分组
                                                _scrollToSection(sectionCode);
                                              });
                                              setModelState(() {});
                                            },
                                            child: Container(
                                              width: 18.sp,
                                              height: 18.sp,
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                color: sectionCode == "C"
                                                    ? ColorConstants
                                                        .mainThemeColor
                                                    : Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(9.sp),
                                              ),
                                              child: Text(
                                                'C',
                                                style: TextStyle(
                                                    color: sectionCode == "C"
                                                        ? Colors.white
                                                        : ColorConstants
                                                            .darkTextColor,
                                                    fontSize: 11.sp,
                                                    fontWeight:
                                                        FontWeight.w500),
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  );
                },
                child: Container(
                  color: Colors.transparent,
                  child: Row(
                    children: [
                      Text(
                        '+$regionCode',
                        style: TextStyle(
                            fontSize: 16.sp,
                            color: ColorConstants.darkTextColor),
                      ),
                      SizedBox(width: 15.sp),
                      Image.asset(
                        R.assetsImagesLoginRegionArrawDown,
                        width: 24.sp,
                        height: 24.sp,
                      ),
                      SizedBox(width: 8.sp),
                      Container(
                        color: const Color(0xFFE5E6EB),
                        width: 0.5.sp,
                        height: 20.sp,
                      ),
                      SizedBox(width: 16.sp),
                    ],
                  ),
                ),
              ),
              SizedBox(
                width: 220.sp,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: 185.sp,
                      child: CupertinoTextField(
                        padding: EdgeInsets.symmetric(vertical: 10.sp),
                        controller: widget.controller,
                        maxLines: 1,
                        maxLength: mobileMaxLength,
                        enabled: true,
                        keyboardType: widget.keyboardType,
                        inputFormatters: widget.inputFormatters,
                        style: TextStyle(
                          color: widget.color,
                          fontSize: widget.fontSize,
                          fontWeight: FontWeight.normal,
                        ),
                        placeholder: widget.placeholder,
                        placeholderStyle: TextStyle(
                          fontSize: widget.fontSize,
                          color: const Color(0xFFC9CDD4),
                          fontWeight: FontWeight.normal,
                        ),
                        decoration: null,
                        textAlign: TextAlign.left,
                        onEditingComplete: () {
                          if (widget.controller.text.length < 5) {
                            showErrorTips = true;
                          } else {
                            showErrorTips = false;
                          }
                        },
                        onChanged: (String text) {
                          setState(() {
                            showDel = text.isNotEmpty;
                          });
                          if (widget.onEditChanged != null) {
                            widget.onEditChanged!(text);
                          }
                        },
                        autocorrect: false,
                      ),
                    ),
                    Visibility(
                      visible: showDel,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            widget.controller.text = '';
                          });
                        },
                        child: Image.asset(
                          R.assetsImagesLoginIconDel,
                          width: 24.sp,
                          height: 24.sp,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        /// 分割线
        Divider(
          color: const Color(0xFFE5E6EB),
          height: 0.5.sp,
        ),

        /// 输入错误提示
        Container(
          margin: EdgeInsets.only(top: 2.sp),
          height: 18.sp,
          child: Text(
            showErrorTips ? '手机号错误' : '',
            style: TextStyle(
                color: ColorConstants.errorTextColor, fontSize: 10.sp),
          ),
        ),
      ],
    );
  }

  void _scrollToSection(String section) {
    final GlobalKey? key = _headerKeys[section];
    if (key != null && key.currentContext != null) {
      // 滚动到分组标题顶部
      Scrollable.ensureVisible(
        key.currentContext!,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        alignment: 0.0, // 0.0 表示滚动到屏幕顶部
      );
    }
  }

  Widget _buildScrollListViewItems(BuildContext context,
      List regionList, StateSetter setModalState) {
    // 展开数据：将分组标题和国家项合并为一个线性列表
    final List<Map<String, dynamic>> allItems = [];
    for (final group in regionList) {
      allItems.add({"type": "header", "key": group["itemKey"]});
      for (final country in group["itemList"]) {
        allItems.add({"type": "items", "countries": country});
      }
    }

    return ListView.builder(
      itemCount: allItems.length,
      itemBuilder: (context, index) {
        final item = allItems[index];
        if (item["type"] == "header") {
          // 绘制分组标题
          return _buildHeader(item["key"]);
        } else {
          // 绘制国家项
          return _buildCountryItem(item["countries"], setModalState);
        }
      },
    );
  }

  /// 创建头部Header
  Widget _buildHeader(String title) {
    return Container(
      key: _headerKeys[title], // 绑定 GlobalKey
      margin: EdgeInsets.symmetric(vertical: 10.sp),
      child: Text(title,
          style: TextStyle(
              color: ColorConstants.darkTextColor,
              fontSize: 16.sp,
              fontWeight: FontWeight.w500)),
    );
  }

  /// 国家项组件
  Widget _buildCountryItem(
    Map<String, dynamic> regionInfo,
    StateSetter setModalState,
  ) {
    return GestureDetector(
      onTap: () {
        setState(() {
          regionCode = regionInfo['code']!;
          if (regionCode == "86") {
            mobileMaxLength = 11;
            if (widget.controller.text.length > 11) {
              widget.controller.text = widget.controller.text.substring(0, 11);
            }
          }
          else {
            mobileMaxLength = 15;
          }
        });
        setModalState(() {});
        Navigator.pop(context);
        if (widget.onRegionChanged != null) {
          widget.onRegionChanged!(regionCode);
        }
      },
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.symmetric(vertical: 16.sp),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text((regionInfo['country']) ?? '',
                style: TextStyle(
                    color: regionCode == regionInfo['code']
                        ? ColorConstants.mainThemeColor
                        : ColorConstants.darkTextColor,
                    fontSize: 14.sp,
                    fontWeight: regionCode == regionInfo['code']
                        ? FontWeight.w500
                        : FontWeight.w400)),
            Container(
              width: 94.sp,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(regionInfo['code'] ?? '',
                      style: TextStyle(
                          color: regionCode == regionInfo['code']
                              ? ColorConstants.mainThemeColor
                              : ColorConstants.darkTextColor,
                          fontSize: 14.sp)),
                  regionCode == regionInfo['code']
                      ? Image.asset(
                          R.assetsImagesLoginRegionChoose,
                          width: 24.sp,
                          height: 24.sp,
                        )
                      : SizedBox(
                          width: 24.sp,
                        ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
