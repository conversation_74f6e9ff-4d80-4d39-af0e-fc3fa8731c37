/*
 * ProjectName：UaApp
 * FilePath：lib/pages/login/widgets
 * FileName：password_input_field
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/18 16:07:25
 */
import 'package:auapp/pub/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../r.dart';

class PasswordInputField extends StatefulWidget {

  final TextEditingController controller;
  final TextInputType keyboardType;
  final String labelText;
  final String placeholder;
  final Color color;
  final double fontSize;
  final List<TextInputFormatter>? inputFormatters;
  final Function(String text)? onEditChanged;

  const PasswordInputField({
    super.key,
    required this.controller,
    this.keyboardType = TextInputType.text,
    this.labelText = '',
    this.placeholder = '请输入',
    this.color = ColorConstants.darkTextColor,
    this.fontSize = 16.0,
    this.inputFormatters,
    this.onEditChanged,
  });

  @override
  State<PasswordInputField> createState() => _PasswordInputFieldState();
}

class _PasswordInputFieldState extends State<PasswordInputField> {
  bool password = true;
  bool showDel = false;

  @override
  Widget build(BuildContext context) {
    showDel = widget.controller.text.isNotEmpty;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 22.sp,
          alignment: Alignment.centerLeft,
          margin: EdgeInsets.only(bottom: 6.sp),
          child: Text(
            widget.labelText,
            style: TextStyle(
              fontSize: 14.sp,
              color: widget.color,
              fontWeight: FontWeight.normal,
            ),
          ),
        ),
        Container(
          height: 44.sp,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 266.sp,
                child: TextField(
                  controller: widget.controller,
                  maxLines: 1,
                  enabled: true,
                  keyboardType: widget.keyboardType,
                  inputFormatters: widget.inputFormatters,
                  style: TextStyle(
                    color: widget.color,
                    fontSize: widget.fontSize,
                    fontWeight: FontWeight.normal,
                  ),
                  textAlign: TextAlign.left,
                  onChanged: (text) {
                    setState(() {
                      showDel = text.isNotEmpty;
                    });
                    if (widget.onEditChanged != null) {
                      widget.onEditChanged!(text);
                    }
                  },
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 10.0),
                    fillColor: Colors.transparent,
                    hintText: widget.placeholder,
                    hintStyle: TextStyle(
                      fontSize: widget.fontSize,
                      color: const Color(0xFFC9CDD4),
                      fontWeight: FontWeight.normal,
                    ),
                    floatingLabelBehavior: FloatingLabelBehavior.always,
                    filled: true,
                    isDense: true,
                  ),
                  obscureText: password,
                  autocorrect: false,
                ),
              ),
              Container(
                child: Row(
                  children: [
                    Visibility(
                      visible: showDel,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            widget.controller.text = '';
                          });
                        },
                        child: Image.asset(
                          R.assetsImagesLoginIconDel,
                          width: 24.sp,
                          height: 24.sp,
                        ),
                      ),
                    ),
                    SizedBox(width: 8.sp),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          password = !password;
                        });
                      },
                      child: Image.asset(
                        password
                            ? R.assetsImagesLoginPwdCipher
                            : R.assetsImagesLoginPwdPlain,
                        width: 24.sp,
                        height: 24.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Divider(
          color: const Color(0xFFE5E6EB),
          height: 0.5.sp,
        ),
      ],
    );
  }
}
