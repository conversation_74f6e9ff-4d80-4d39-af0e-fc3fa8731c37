/*
 * ProjectName：UaApp
 * FilePath：lib/pages/login/widgets
 * FileName：login_app_bar
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/19 11:25:54
 */
import 'package:flutter/material.dart';

class LoginAppBar {
  static AppBar appBar(
      BuildContext context, String title, IconData? backIcon, Color color,
      {void Function()? callback,  List<Widget>? actions, Color? backgroundColor}) {
    return AppBar(
      leading: backIcon == null ? null : IconButton(
        icon: Icon(backIcon, color: color),
        onPressed: () {
          if (callback != null) {
            callback();
          } else {
            Navigator.pop(context);
          }
        },
      ),
      centerTitle: true,
      title: Text(
        title,
        style: TextStyle(color: color, fontFamily: 'Rubik'),
      ),
      backgroundColor: backgroundColor ?? Colors.white,
      elevation: 0.0,
      actions: actions,
    );
  }
}
