/*
 * ProjectName：UaApp
 * FilePath：lib/pages/login/widgets
 * FileName：verification_code_input_filed
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/18 16:08:13
 */

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../pub/constants/color_constants.dart';
import '../../../r.dart';

class VerificationCodeInputFiled extends StatefulWidget {

  final TextEditingController controller;
  final TextInputType keyboardType;
  final String labelText;
  final String buttonTitle;
  final bool isCounting;
  final String placeholder;
  final Color color;
  final double fontSize;
  final int maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final VoidCallback? onPressed;
  final Function(String text)? onEditChanged;

  const VerificationCodeInputFiled({
    super.key,
    required this.controller,
    this.keyboardType = TextInputType.text,
    this.labelText = '',
    required this.buttonTitle,
    required this.isCounting,
    this.placeholder = '',
    this.color = ColorConstants.darkTextColor,
    this.fontSize = 16.0,
    this.maxLength = 6,
    this.inputFormatters,
    this.onPressed,
    this.onEditChanged,
  });

  @override
  State<VerificationCodeInputFiled> createState() => _VerificationCodeInputFiledState();
}

class _VerificationCodeInputFiledState extends State<VerificationCodeInputFiled> {
  bool showDel = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    showDel = widget.controller.text.isNotEmpty;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 22.sp,
          alignment: Alignment.centerLeft,
          margin: EdgeInsets.only(bottom: 6.sp),
          child: Text(
            widget.labelText,
            style: TextStyle(
              fontSize: 14.sp,
              color: widget.color,
              fontWeight: FontWeight.normal,
            ),
          ),
        ),
        Container(
          height: 44.sp,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 160.sp,
                child: CupertinoTextField(
                  padding: EdgeInsets.symmetric(vertical: 10.sp),
                  controller: widget.controller,
                  maxLines: 1,
                  maxLength: widget.maxLength,
                  enabled: true,
                  keyboardType: widget.keyboardType,
                  inputFormatters: widget.inputFormatters,
                  style: TextStyle(
                    color: widget.color,
                    fontSize: widget.fontSize,
                    fontWeight: FontWeight.normal,
                  ),
                  textAlign: TextAlign.left,
                  onChanged: (text) {
                    setState(() {
                      showDel = text.isNotEmpty;
                    });
                    if (widget.onEditChanged != null) {
                      widget.onEditChanged!(text);
                    }
                  },
                  placeholder: widget.placeholder,
                  placeholderStyle: TextStyle(
                    fontSize: widget.fontSize,
                    color: const Color(0xFFC9CDD4),
                    fontWeight: FontWeight.normal,
                  ),
                  decoration: null,
                  autocorrect: false,
                ),
              ),
              Container(
                child: Row(
                  children: [
                    Visibility(
                      visible: showDel,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            widget.controller.text = '';
                          });
                        },
                        child: Image.asset(
                          R.assetsImagesLoginIconDel,
                          width: 24.sp,
                          height: 24.sp,
                        ),
                      ),
                    ),
                    SizedBox(width: 8.sp),
                    GestureDetector(
                      onTap: () {
                        if (widget.onPressed != null) {
                          if (widget.isCounting) return;

                          // _startCountdown();
                          widget.onPressed?.call();
                        }
                      },
                      child: Text(widget.buttonTitle, style: TextStyle(fontSize: 16.sp, color: widget.isCounting ? const Color(0xFF9DE2FB) : ColorConstants.mainThemeColor),),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Divider(
          color: const Color(0xFFE5E6EB),
          height: 0.5.sp,
        ),
      ],
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

}
