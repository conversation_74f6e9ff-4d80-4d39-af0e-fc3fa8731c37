/*
 * ProjectName：UaApp
 * FilePath：lib/pages/login/widgets
 * FileName：email_input_field
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/18 16:07:01
 */
import 'package:auapp/pages/login/utils/login_validator.dart';
import 'package:auapp/pub/constants/color_constants.dart';
import 'package:auapp/utils/app_focus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../r.dart';

class EmailInputField extends StatefulWidget {
  final TextEditingController controller;
  final TextInputType keyboardType;
  final String labelText;
  final String placeholder;
  final Color color;
  final double fontSize;
  final List<TextInputFormatter>? inputFormatters;
  final Function(String text)? onEditChanged;

  const EmailInputField({
    super.key,
    required this.controller,
    this.keyboardType = TextInputType.text,
    this.labelText = '',
    this.placeholder = '请输入',
    this.color = ColorConstants.darkTextColor,
    this.fontSize = 16.0,
    this.inputFormatters,
    this.onEditChanged,
  });

  @override
  State<EmailInputField> createState() => _EmailInputFieldState();
}

class _EmailInputFieldState extends State<EmailInputField> {
  bool showErrorTips = false;
  bool showDel = false;

  @override
  Widget build(BuildContext context) {
    showDel = widget.controller.text.isNotEmpty;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 22.sp,
          alignment: Alignment.centerLeft,
          margin: EdgeInsets.only(bottom: 6.sp),
          child: Text(
            widget.labelText,
            style: TextStyle(
              fontSize: 14.sp,
              color: widget.color,
              fontWeight: FontWeight.normal,
            ),
          ),
        ),
        SizedBox(
          height: 44.sp,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 290.sp,
                child: TextField(
                  controller: widget.controller,
                  maxLines: 1,
                  enabled: true,
                  keyboardType: widget.keyboardType,
                  inputFormatters: widget.inputFormatters,
                  style: TextStyle(
                    color: widget.color,
                    fontSize: widget.fontSize,
                    fontWeight: FontWeight.normal,
                  ),
                  onEditingComplete: () {
                    if (!LoginValidator.validateEmail(widget.controller.text)) {
                      setState(() {
                        showErrorTips = true;
                      });
                    } else {
                      setState(() {
                        showErrorTips = false;
                      });
                    }
                    AppFocus.unfocus(context);
                  },
                  onChanged: (String text) {
                    setState(() {
                      showDel = text.isNotEmpty;
                    });
                    if (widget.onEditChanged != null) {
                      widget.onEditChanged!(text);
                    }
                  },
                  textAlign: TextAlign.left,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 10.0),
                    fillColor: Colors.transparent,
                    hintText: widget.placeholder,
                    hintStyle: TextStyle(
                      fontSize: widget.fontSize,
                      color: const Color(0xFFC9CDD4),
                      fontWeight: FontWeight.normal,
                    ),
                    floatingLabelBehavior: FloatingLabelBehavior.always,
                    filled: true,
                    isDense: true,
                  ),
                  autocorrect: false,
                ),
              ),
              Visibility(
                visible: showDel,
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      widget.controller.text = '';
                    });
                  },
                  child: Image.asset(
                    R.assetsImagesLoginIconDel,
                    width: 24.sp,
                    height: 24.sp,
                  ),
                ),
              )
            ],
          ),
        ),
        Divider(
          color: const Color(0xFFE5E6EB),
          height: 0.5.sp,
        ),

        /// 输入错误提示
        Container(
          margin: EdgeInsets.only(top: 2.sp),
          height: 18.sp,
          child: Text(
            showErrorTips ? 'login_email_error'.tr : '',
            style: TextStyle(
                color: ColorConstants.errorTextColor, fontSize: 10.sp),
          ),
        ),
      ],
    );
  }
}
