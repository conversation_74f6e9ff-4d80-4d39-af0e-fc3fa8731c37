import 'package:auapp/pages/login/widgets/email_input_field.dart';
import 'package:auapp/pages/login/widgets/login_app_bar.dart';
import 'package:auapp/pages/login/widgets/mobile_input_field.dart';
import 'package:auapp/pages/login/widgets/password_input_field.dart';
import 'package:auapp/pages/login/widgets/verification_code_input_filed.dart';
import 'package:auapp/pub/constants/color_constants.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/utils/app_focus.dart';
import 'package:auapp/utils/hp_input_formater.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:auapp/utils/hp_localization.dart';

import '../../pub/constants/storage_constants.dart';
import '../../r.dart';
import '../../services/storage_service.dart';
import '../../widgets/hp_border_button.dart';
import 'login_controller.dart';

class LoginPage extends GetView<LoginController> {
  final GlobalKey<FormState> loginFormKey = GlobalKey<FormState>();

  LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    // controller.loginEmailController.text = "<EMAIL>";
    // controller.loginPasswordController.text = "h123456";
    return Scaffold(
      appBar: LoginAppBar.appBar(
        context,
        '',
        null,
        Colors.transparent,
        // actions: [_buildLanguageChooseWidgets(context)],
      ),
      resizeToAvoidBottomInset: false, // 默认值为 true，自动调整布局
      backgroundColor: Colors.white,
      body: GestureDetector(
        onTap: () {
          AppFocus.unfocus(context);
        },
        child: Column(
          children: [
            Expanded(
              child: _buildForms(context),
            ),
            _buildBottom(context),
          ],
        ),
      ),
    );
  }

  /// 选择语言
  Widget _buildLanguageChooseWidgets(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showModalBottomSheet(
          context: context,
          // 设置顶部圆角
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16.sp)),
          ),
          backgroundColor: Colors.white, // 背景颜色
          isScrollControlled: true, // 允许滚动（内容较长时）
          builder: (context) => Container(
            height: 350.sp,
            padding: EdgeInsets.all(16.sp),
            child: Column(
              children: [
                // 标题和关闭按钮
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: 24.sp,
                    ),
                    Text(
                      'login_select_language'.tr,
                      style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w500,
                          color: ColorConstants.darkTextColor),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Image.asset(
                        R.assetsImagesLoginPopupClose,
                        width: 24.sp,
                        height: 24.sp,
                      ),
                    ),
                  ],
                ),
                // 滚动内容
                Expanded(
                  child: ListView.builder(
                    itemCount: controller.languageList.length,
                    itemBuilder: (context, index) => GestureDetector(
                      onTap: () {
                        HPLocalization.localize(controller.languageList[index]
                                ['language'] ??
                            'en-US');
                        controller.currentRegion.value =
                            controller.languageList[index]['language'] ==
                                    'zh-CN'
                                ? 'CN'
                                : 'Au';
                        controller.currentRegionIcon.value =
                            controller.languageList[index]['icon'] ?? '';
                        Navigator.pop(context);
                      },
                      child: Container(
                        color: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 8.sp),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Container(
                                  width: 38.sp,
                                  height: 38.sp,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(19.sp)),
                                      color: Color(0xFFF5F5F5)),
                                  child: Image.asset(
                                    controller.languageList[index]['icon'] ??
                                        '',
                                    width: 24.sp,
                                    height: 24.sp,
                                  ),
                                ),
                                SizedBox(width: 12.sp),
                                Container(
                                  child: Text(
                                      controller.languageList[index]
                                              ['region'] ??
                                          '',
                                      style: TextStyle(
                                          color: ColorConstants.darkTextColor,
                                          fontSize: 14.sp)),
                                )
                              ],
                            ),
                            Obx(() => Visibility(
                                  visible: controller.languageList[index]
                                          ['language'] ==
                                      StorageService.getString(
                                          StorageConstants.huepayLanguage),
                                  child: Image.asset(
                                    R.assetsImagesLoginRegionChoose,
                                    width: 24.sp,
                                    height: 24.sp,
                                  ),
                                )),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(right: 12),
        padding: const EdgeInsets.all(8),
        color: Colors.white,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              controller.currentRegionIcon.value,
              width: 12,
              height: 12,
            ),
            const SizedBox(width: 8),
            Text(
              controller.currentRegion.value,
              style: TextStyle(
                fontSize: 12.sp,
                color: Color(0xFF464B4E),
              ),
            ),
            const SizedBox(width: 14),
            Image.asset(
              R.assetsImagesLoginArrawDown,
              width: 12,
              height: 12,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildForms(BuildContext context) {
    return Form(
      key: loginFormKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 24.sp),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              margin: EdgeInsets.only(top: 16.sp),
              height: 34.sp,
              child: Obx(() => Text(
                  controller.loginAccountType.value == 'MP'
                      ? 'login_account_type_mobile'.tr
                      : 'login_account_type_email'.tr,
                  style: TextStyle(
                      fontSize: 24.sp,
                      color: ColorConstants.darkTextColor,
                      fontWeight: FontWeight.w500))),
            ),
            SizedBox(height: 40.sp),
            Obx(() => controller.loginAccountType.value == 'MP'
                ? MobileInputField(
                    controller: controller.loginMobileController,
                    keyboardType: TextInputType.number,
                    initialRegionCode: controller.regionCode.value,
                    inputFormatters: [HPInputFormatter.inputNumberFormatter],
                    fontSize: 16.sp,
                    labelText: 'login_mobile_tips'.tr,
                    placeholder: 'login_input_mobile_placeholder'.tr,
                    onRegionChanged: (regionCode) {
                      print('地区发生了变化：$regionCode');
                      controller.regionCode.value = regionCode;
                    },
                    onEditChanged: (text) {
                      if (!TextUtil.isEmpty(text)) {
                        controller.mobileInputEnable.value = true;
                      } else {
                        controller.mobileInputEnable.value = false;
                      }
                    },
                  )
                : EmailInputField(
                    controller: controller.loginEmailController,
                    keyboardType: TextInputType.text,
                    fontSize: 16.sp,
                    labelText: 'login_email_address_tips'.tr,
                    placeholder: 'login_input_email_placeholder'.tr,
                    onEditChanged: (text) {
                      if (!TextUtil.isEmpty(text)) {
                        controller.emailInputEnable.value = true;
                      } else {
                        controller.emailInputEnable.value = false;
                      }
                    },
                  )),
            SizedBox(height: 8.sp),
            Obx(() => controller.loginPwdType.value == 'Pwd'
                ? PasswordInputField(
                    controller: controller.loginPasswordController,
                    keyboardType: TextInputType.text,
                    labelText: 'login_password_tips'.tr,
                    placeholder: 'login_input_password_placeholder'.tr,
                    inputFormatters: [
                      HPInputFormatter.denyChineseAndEmojiFormatter,
                    ],
                    onEditChanged: (text) {
                      if (!TextUtil.isEmpty(text)) {
                        controller.pwdInputEnable.value = true;
                      } else {
                        controller.pwdInputEnable.value = false;
                      }
                    },
                  )
                : VerificationCodeInputFiled(
                    controller: controller.loginVerifyCodeController,
                    keyboardType: TextInputType.number,
                    labelText: 'login_verify_code'.tr,
                    buttonTitle: controller.verifyCodeBtnTitle.value,
                    isCounting: controller.isCounting.value,
                    inputFormatters: [HPInputFormatter.inputNumberFormatter],
                    placeholder: 'login_verify_code_placeholder'.tr,
                    onPressed: () {
                      controller.sendVerifyCode();
                    },
                    onEditChanged: (text) {
                      if (!TextUtil.isEmpty(text)) {
                        controller.verifyCodeInputEnable.value = true;
                      } else {
                        controller.verifyCodeInputEnable.value = false;
                      }
                    },
                  )),
            SizedBox(height: 20.sp),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () {
                    controller.loginPwdType.value =
                        controller.loginPwdType.value == 'Pwd' ? 'Code' : 'Pwd';
                  },
                  child: Container(
                    width: 150.sp,
                    height: 22.sp,
                    alignment: Alignment.centerLeft,
                    child: Obx(() => Text(
                          controller.loginPwdType.value == 'Pwd'
                              ? 'login_pwd_type_code'.tr
                              : 'login_pwd_type_password'.tr,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: ColorConstants.mainThemeColor,
                          ),
                        )),
                  ),
                ),
                // Obx(() => Visibility(
                //       visible: controller.loginPwdType.value == 'Pwd',
                //       child: GestureDetector(
                //         onTap: () {},
                //         child: Container(
                //           width: 150.sp,
                //           height: 22.sp,
                //           alignment: Alignment.centerRight,
                //           child: Text(
                //             'login_forget_pwd_title'.tr,
                //             style: TextStyle(
                //               fontSize: 14.sp,
                //               color: const Color(0xFF464B4E),
                //             ),
                //           ),
                //         ),
                //       ),
                //     )),
              ],
            ),
            SizedBox(
              height: 28.sp,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    controller.chooseBoxFlag.value =
                        !controller.chooseBoxFlag.value;
                  },
                  child: Obx(() => Image.asset(
                        controller.chooseBoxFlag.value
                            ? R.assetsImagesLoginChooseBoxSel
                            : R.assetsImagesLoginChooseBoxUnsel,
                        width: 24.sp,
                        height: 24.sp,
                      )),
                ),
                Container(
                  height: 24.sp,
                  alignment: Alignment.center,
                  child: Text(
                    'login_protocol_tips'.tr,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: Color(0xFFA7ADB0),
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    print('点击了《隐私政策》');
                    HPRouter.pushPage(Routes.webView, parameters: {
                      'url':
                          "https://cdn.huepay.com.au/huepayapp/privacy_policy.html",
                      'title': 'Privacy Policy',
                    });
                  },
                  child: Container(
                    height: 24.sp,
                    alignment: Alignment.center,
                    child: Text(
                      'login_protocol_privacy'.tr,
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: ColorConstants.mainThemeColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.sp),
            Obx(() => HPBorderButton(
                  text: 'login_button_title'.tr,
                  backgroundColor: (controller.chooseBoxFlag.value &&
                          ((controller.loginAccountType.value == 'EMAIL'
                                  ? controller.emailInputEnable.value
                                  : controller.mobileInputEnable.value) &&
                              (controller.loginPwdType.value == 'Pwd'
                                  ? controller.pwdInputEnable.value
                                  : controller.verifyCodeInputEnable.value)))
                      ? ColorConstants.mainThemeColor
                      : ColorConstants.mainDisableThemeColor,
                  onPressed: () {
                    AppFocus.unfocus(context);
                    if (!(controller.chooseBoxFlag.value &&
                        ((controller.loginAccountType.value == 'EMAIL'
                                ? controller.emailInputEnable.value
                                : controller.mobileInputEnable.value) &&
                            (controller.loginPwdType.value == 'Pwd'
                                ? controller.pwdInputEnable.value
                                : controller.verifyCodeInputEnable.value)))) {
                      return;
                    }
                    controller.login(context);
                  },
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildBottom(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 24.sp,
        vertical: 12.sp,
      ),
      child: SafeArea(
        // 处理安全区域（如刘海屏）
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Container(
                    height: 0.5.sp,
                    color: const Color(0xFFE5E6EB),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.sp), // 设置左右间距
                  child: Container(
                    height: 20.sp,
                    alignment: Alignment.center,
                    child: Text(
                      'login_change_login_type_title'.tr,
                      style: const TextStyle(color: Color(0xFFA7ADB0)),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 0.5.sp,
                    color: const Color(0xFFE5E6EB),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 12.sp,
            ),
            GestureDetector(
              onTap: () {
                /// 切换登录方式
                controller.changeLoginMethods();
              },
              child: Container(
                height: 48.sp,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(3.sp),
                  border: Border.all(
                    color: const Color(0xFFE5E6EB),
                    width: 0.5.sp,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Obx(() => Image.asset(
                          controller.loginAccountType.value == 'EMAIL'
                              ? R.assetsImagesLoginIconMobile
                              : R.assetsImagesLoginIconEmail,
                          width: 16.sp,
                          height: 16.sp,
                        )),
                    SizedBox(width: 8.sp),
                    Obx(() => Text(
                          controller.loginAccountType.value == 'EMAIL'
                              ? 'login_account_type_mobile_bottom'.tr
                              : 'login_account_type_email_bottom'.tr,
                          style: TextStyle(
                              color: ColorConstants.darkTextColor,
                              fontSize: 16.sp),
                        )),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
