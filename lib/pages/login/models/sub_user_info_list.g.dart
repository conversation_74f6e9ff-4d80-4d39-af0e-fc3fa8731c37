// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sub_user_info_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubUserInfoList _$SubUserInfoListFromJson(Map<String, dynamic> json) =>
    SubUserInfoList(
      sub_user_info_list: (json['sub_user_info_list'] as List<dynamic>?)
          ?.map((e) => SubUserInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SubUserInfoListToJson(SubUserInfoList instance) =>
    <String, dynamic>{
      'sub_user_info_list': instance.sub_user_info_list,
    };

SubUserInfo _$SubUserInfoFromJson(Map<String, dynamic> json) => SubUserInfo(
      sub_user_id: json['sub_user_id'] as String?,
      status: json['status'] as String?,
      is_edit_pwd: json['is_edit_pwd'] as String?,
      org_id: json['org_id'] as String?,
      cust_id: json['cust_id'] as String?,
      cust_reg_name: json['cust_reg_name'] as String?,
      cust_en_name: json['cust_en_name'] as String?,
      cust_short_name: json['cust_short_name'] as String?,
      cust_status: json['cust_status'] as String?,
    );

Map<String, dynamic> _$SubUserInfoToJson(SubUserInfo instance) =>
    <String, dynamic>{
      'sub_user_id': instance.sub_user_id,
      'status': instance.status,
      'is_edit_pwd': instance.is_edit_pwd,
      'org_id': instance.org_id,
      'cust_id': instance.cust_id,
      'cust_reg_name': instance.cust_reg_name,
      'cust_en_name': instance.cust_en_name,
      'cust_short_name': instance.cust_short_name,
      'cust_status': instance.cust_status,
    };
