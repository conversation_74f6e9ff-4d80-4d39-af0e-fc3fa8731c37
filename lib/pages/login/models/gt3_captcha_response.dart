/*
 * ProjectName：UaApp
 * FilePath：lib/pages/login/models
 * FileName：gt3_captcha_response
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/19 18:53:19
 */
import 'package:json_annotation/json_annotation.dart';

part 'gt3_captcha_response.g.dart';

/// 标记该类需要生成 JSON 序列化代码
@JsonSerializable()
class Gt3CaptchaResponse {
  /// 所属类型：1-行为验证码 2-常规验证码
  final String? category;
  /// 用户唯一标识
  final String? capt_user_id;

  /// 行为验证流水号
  final String? challenge;

  /// 行为验证ID category 为 1-行为验证码 时返回
  final String? gt;

  /// 新验证码(宕机情况下使用，表示验证是3.0还是2.0，3.0的sdk该字段为true，category值为1时返回)
  final bool? new_captcha;

  /// 服务器是否宕机标志(行为验证码API服务器是否宕机（即处于fallback状态），category值为1时返回)
  final int? success;

  Gt3CaptchaResponse({
    this.category,
    this.capt_user_id,
    this.challenge,
    this.gt,
    this.new_captcha,
    this.success
  });

  /// 从 JSON 创建 对象（自动生成）
  factory Gt3CaptchaResponse.fromJson(Map<String, dynamic> json) => _$Gt3CaptchaResponseFromJson(json);

  /// 将 对象转为 JSON（自动生成）
  Map<String, dynamic> toJson() => _$Gt3CaptchaResponseToJson(this);
}
