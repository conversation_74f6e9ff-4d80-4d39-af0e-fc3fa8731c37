// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginRequest _$LoginRequestFromJson(Map<String, dynamic> json) => LoginRequest(
      login_account_type: json['login_account_type'] as String,
      mp_area_code: json['mp_area_code'] as String?,
      mp: json['mp'] as String?,
      email: json['email'] as String?,
      password: json['password'] as String?,
      captcha: json['captcha'] as String?,
      capt_check_info: CaptCheckInfo.fromJson(
          json['capt_check_info'] as Map<String, dynamic>),
      app_info: AppInfo.fromJson(json['app_info'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LoginRequestToJson(LoginRequest instance) =>
    <String, dynamic>{
      'login_account_type': instance.login_account_type,
      'mp_area_code': instance.mp_area_code,
      'mp': instance.mp,
      'email': instance.email,
      'password': instance.password,
      'captcha': instance.captcha,
      'capt_check_info': instance.capt_check_info,
      'app_info': instance.app_info,
    };

AppInfo _$AppInfoFromJson(Map<String, dynamic> json) => AppInfo(
      app_platform: json['app_platform'] as String,
      app_version: json['app_version'] as String?,
      device_id: json['device_id'] as String?,
    );

Map<String, dynamic> _$AppInfoToJson(AppInfo instance) => <String, dynamic>{
      'app_platform': instance.app_platform,
      'app_version': instance.app_version,
      'device_id': instance.device_id,
    };
