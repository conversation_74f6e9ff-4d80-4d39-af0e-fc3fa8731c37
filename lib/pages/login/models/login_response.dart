/*
 * ProjectName：UaApp
 * FilePath：lib/pages/login/models/login_response.dart
 * FileName：login_response
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/16 18:41:19
 */
import 'package:json_annotation/json_annotation.dart';

part 'login_response.g.dart';

/// 标记该类需要生成 JSON 序列化代码
@JsonSerializable()
class LoginResponse {
  final int count;
  final String hf_token;

  LoginResponse({
    this.count = 0,
    this.hf_token = "",
  });

  /// 从 JSON 创建 LoginResponse 对象（自动生成）
  factory LoginResponse.fromJson(Map<String, dynamic> json) => _$LoginResponseFromJson(json);

  /// 将 LoginResponse 对象转为 JSON（自动生成）
  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);
}
