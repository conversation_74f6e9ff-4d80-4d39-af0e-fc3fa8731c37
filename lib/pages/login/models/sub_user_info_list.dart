/*
 * ProjectName：UaApp
 * FilePath：lib/pages/login/models
 * FileName：sub_user_info_list
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/20 15:01:41
 */

import 'package:json_annotation/json_annotation.dart';

part 'sub_user_info_list.g.dart';

/// 标记该类需要生成 JSON 序列化代码
@JsonSerializable()
class SubUserInfoList {

  final List<SubUserInfo>? sub_user_info_list;

  SubUserInfoList({
    this.sub_user_info_list,
  });

  factory SubUserInfoList.fromJson(Map<String, dynamic> json) =>
      _$SubUserInfoListFromJson(json);
  Map<String, dynamic> toJson() => _$SubUserInfoListToJson(this);

}

@JsonSerializable()
class SubUserInfo {
  /// 子用户编号
  final String? sub_user_id;

  /// 子用户状态:N-正常(Normal) F-冻结(Frozen) C-注销(Cancel)
  final String? status;

  /// 是否需要首次修改密码(Y-是 N-否)
  final String? is_edit_pwd;

  /// 组织编号
  final String? org_id;

  /// 客户号
  final String? cust_id;

  /// 客户注册名称
  final String? cust_reg_name;

  /// 客户英文名称
  final String? cust_en_name;

  /// 客户简称
  final String? cust_short_name;

  /// 客户状态(N-正常(Normal) C-关闭(Closed))
  final String? cust_status;


  SubUserInfo({
    this.sub_user_id,
    this.status,
    this.is_edit_pwd,
    this.org_id,
    this.cust_id,
    this.cust_reg_name,
    this.cust_en_name,
    this.cust_short_name,
    this.cust_status
});

  factory SubUserInfo.fromJson(Map<String, dynamic> json) =>
      _$SubUserInfoFromJson(json);
  Map<String, dynamic> toJson() => _$SubUserInfoToJson(this);
}
