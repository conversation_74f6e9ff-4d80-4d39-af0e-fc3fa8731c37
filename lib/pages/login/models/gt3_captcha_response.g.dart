// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gt3_captcha_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Gt3CaptchaResponse _$Gt3CaptchaResponseFromJson(Map<String, dynamic> json) =>
    Gt3CaptchaResponse(
      category: json['category'] as String?,
      capt_user_id: json['capt_user_id'] as String?,
      challenge: json['challenge'] as String?,
      gt: json['gt'] as String?,
      new_captcha: json['new_captcha'] as bool?,
      success: (json['success'] as num?)?.toInt(),
    );

Map<String, dynamic> _$Gt3CaptchaResponseToJson(Gt3CaptchaResponse instance) =>
    <String, dynamic>{
      'category': instance.category,
      'capt_user_id': instance.capt_user_id,
      'challenge': instance.challenge,
      'gt': instance.gt,
      'new_captcha': instance.new_captcha,
      'success': instance.success,
    };
