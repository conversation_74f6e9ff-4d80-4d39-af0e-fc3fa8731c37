/*
 * ProjectName：UaApp
 * FilePath：lib/pages/login/models
 * FileName：capt_check_info
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/20 11:00:12
 */

import 'package:json_annotation/json_annotation.dart';

part 'capt_check_info.g.dart';

@JsonSerializable()
class CaptCheckInfo {
  /// 所属类型(1-行为验证码 2-常规验证码)
  final String category;

  /// 用户唯一标识
  final String capt_user_id;

  /// 行为验证流水号(行为验证所需校验参数)
  final String geetest_challenge;

  /// 行为验证所需校验参数
  final String geetest_validate;

  /// 行为验证所需校验参数
  final String geetest_seccode;

  CaptCheckInfo({
    required this.category,
    required this.capt_user_id,
    required this.geetest_challenge,
    required this.geetest_validate,
    required this.geetest_seccode
  });

  factory CaptCheckInfo.fromJson(Map<String, dynamic> json) => _$CaptCheckInfoFromJson(json);
  Map<String, dynamic> toJson() => _$CaptCheckInfoToJson(this);
}
