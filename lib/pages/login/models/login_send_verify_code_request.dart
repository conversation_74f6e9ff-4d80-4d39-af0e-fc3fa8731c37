/*
 * ProjectName：UaApp
 * FilePath：lib/pages/login/models
 * FileName：login_send_verify_code_request
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/19 19:07:02
 */
import 'package:json_annotation/json_annotation.dart';

import 'capt_check_info.dart';

part 'login_send_verify_code_request.g.dart';

/// 标记该类需要生成 JSON 序列化代码
@JsonSerializable()
class LoginSendVerifyCodeRequest {
  /// 登录类型(MP-手机 EMAIL-邮箱账号)
  final String login_account_type;
  /// 手机号地区编码(86-中国大陆地区)
  final String? mp_area_code;

  /// 手机号码(当login_account_type为MP时必填)
  final String? mp;

  /// 邮箱账号(当login_account_type为EMAIL时必填)
  final String? email;

  /// 1-登录 2-忘记密码
  final String captcha_type;

  /// 服务器是否宕机标志(行为验证码API服务器是否宕机（即处于fallback状态），category值为1时返回)
  final CaptCheckInfo capt_check_info;

  LoginSendVerifyCodeRequest({
    required this.login_account_type,
    this.mp_area_code,
    this.mp,
    this.email,
    required this.captcha_type,
    required this.capt_check_info
  });

  /// 从 JSON 创建 对象（自动生成）
  factory LoginSendVerifyCodeRequest.fromJson(Map<String, dynamic> json) => _$LoginSendVerifyCodeRequestFromJson(json);

  /// 将 对象转为 JSON（自动生成）
  Map<String, dynamic> toJson() => _$LoginSendVerifyCodeRequestToJson(this);
}
