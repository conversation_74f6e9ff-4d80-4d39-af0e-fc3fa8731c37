/*
 * ProjectName：UaApp
 * FilePath：lib/pages/login/utils
 * FileName：login_validator
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/20 17:29:04
 */

import 'package:auapp/base/base_binding.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:auapp/utils/hp_regex.dart';
import 'package:common_utils/common_utils.dart';
import 'package:get/get.dart';

class LoginValidator {
  /// 手机号验证
  static bool validateMobile(String mobile, regionCode) {
    if (TextUtil.isEmpty(mobile)) {
      HPLoading.showToast('login_input_mobile_placeholder'.tr);
      return false;
    }

    /// 中国地区手机号
    if (regionCode == "86") {
      if (mobile.length != 11) {
        HPLoading.showToast('login_mobile_format_error'.tr);
        return false;
      }
      if (HPRegex.isMobile(mobile)) {
        return true;
      }
      HPLoading.showToast('login_mobile_format_error'.tr);
      return false;
    }
    /// 其他地区手机号
    else {
      if (mobile.length > 15) {
        HPLoading.showToast('login_mobile_format_error'.tr);
        return false;
      }
      return true;
    }
  }

  /// 邮箱验证
  static bool validateEmail(String email) {
    if (TextUtil.isEmpty(email)) {
      HPLoading.showToast('login_input_email_placeholder'.tr);
      return false;
    }
    if (HPRegex.isEmail(email)) {
      return true;
    }
    HPLoading.showToast('login_email_format_error'.tr);
    return false;
  }

  /// 密码验证
  static bool validatePassword(String password) {
    if (TextUtil.isEmpty(password)) {
      HPLoading.showToast('login_input_password_placeholder'.tr);
      return false;
    }
    return true;
  }

  /// 验证验证码
  static bool validateCode(String code) {
    if (TextUtil.isEmpty(code)) {
      HPLoading.showToast('login_verify_code_placeholder'.tr);
      return false;
    }
    return true;
  }
}
