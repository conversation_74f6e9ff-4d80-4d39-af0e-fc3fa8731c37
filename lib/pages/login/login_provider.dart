/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-16 15:20:55
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-16 15:22:01
 * @FilePath: /hwicc-mobile-flutter/lib/pages/main/main_provider.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';

class LoginProvider {
  /// 登录接口
  Future<HPResponse> login(Map<String, dynamic> data) async {
    // final loginResponse = await MockRequest.mockRequest('login_response.json');
    // return LoginResponse.fromJson(loginResponse);
    HPResponse response = await HttpRequest.post(ApiConstants.loginUrl, params: data);
    return response;
  }

  /// 获取极验图形验证码接口
  Future<HPResponse> getCaptcha() async {
    HPResponse response = await HttpRequest.get(ApiConstants.loginCaptchaUrl);
    return response;
  }

  /// 获取验证码接口
  Future<HPResponse> sendVerifyCode(Map<String, dynamic> data) async {
    HPResponse response = await HttpRequest.post(ApiConstants.sendVerifyCodeUrl, params: data);
    return response;
  }

  /// 获取商户列表接口
  Future<HPResponse> queryMerchantList() async {
    HPResponse response = await HttpRequest.get(ApiConstants.getUserSwitchListUrl);
    return response;
  }

  /// 切换商户接口
  Future<HPResponse> chooseMerchant(Map<String, dynamic> data) async {
    HPResponse response = await HttpRequest.post(ApiConstants.switchUserUrl, params: data);
    return response;
  }
}
