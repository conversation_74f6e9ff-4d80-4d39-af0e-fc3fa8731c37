/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-16 11:00:31
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-16 15:23:25
 * @FilePath: /hwicc-mobile-flutter/lib/pages/login/login_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/pages/login/login_provider.dart';
import 'package:auapp/pages/login/models/gt3_captcha_response.dart';
import 'package:auapp/pages/login/models/login_request.dart';
import 'package:auapp/pages/login/models/login_send_verify_code_request.dart';
import 'package:auapp/pages/login/models/sub_user_info_list.dart';
import 'package:auapp/pages/login/utils/login_validator.dart';
import 'package:auapp/pages/merchantSelect/models/switch_data.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/utils/hp_encrypt.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:auapp/vendor/gt3_captcha_util.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import '../../pub/constants/storage_constants.dart';
import '../../r.dart';
import '../../services/storage_service.dart';
import 'models/capt_check_info.dart';
import 'models/login_response.dart';

class LoginController extends GetxController {
  final LoginProvider apiRepository;
  LoginController({required this.apiRepository});

  final loginEmailController = TextEditingController();
  final loginMobileController = TextEditingController();
  final loginPasswordController = TextEditingController();
  final loginVerifyCodeController = TextEditingController();

  final loginAccountType = "MP".obs; // MP, EMAIL
  final loginPwdType = "Pwd".obs; // Pwd, Code
  final chooseBoxFlag = false.obs; // 选中协议
  final regionCode = "61".obs;
  final languageList = [
    {
      "region": '简体中文',
      "language": 'zh-CN',
      "icon": R.assetsImagesLoginRegionChina,
    },
    {
      "region": 'English',
      "language": 'en-US',
      "icon": R.assetsImagesLoginRegionAustralia,
    },
  ].obs;

  final currentRegion = "EN".obs;
  final currentRegionIcon = R.assetsImagesLoginRegionAustralia.obs;

  late Gt3CaptchaResponse? captchaResponse;
  late AppInfo appInfo;
  late CaptCheckInfo? captCheckInfo;

  late bool hasSendCode;

  String captchaType = "code";

  final verifyCodeBtnTitle = ''.obs;

  late int seconds = 60;         // 倒计时总秒数
  final isCounting = false.obs;  // 倒计时状态标识
  Timer? timer;             // 定时器对象

  final mobileInputEnable = false.obs;
  final emailInputEnable = false.obs;
  final pwdInputEnable = false.obs;
  final verifyCodeInputEnable = false.obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();

    /// 初始语言国际化配置
    String language =
        StorageService.getString(StorageConstants.huepayLanguage) ?? "en-US";
    if (language == "en-US") {
      currentRegion.value = "EN";
      currentRegionIcon.value = R.assetsImagesLoginRegionAustralia;
    }
    else {
      currentRegion.value = "CN";
      currentRegionIcon.value = R.assetsImagesLoginRegionChina;
    }
    hasSendCode = false;
    verifyCodeBtnTitle.value = 'login_send_code_title'.tr;

    seconds = 60;         // 倒计时总秒数
    isCounting.value = false;  // 倒计时状态标识
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();

    onListenGt3Events();

    getAppInfo();
  }

  /// 设备信息
  void getAppInfo() {
    Map deviceInfo = UiSize.deviceInfo;
    String appPlatform = "";
    String deviceId = "";
    if (Platform.isIOS) {
      appPlatform = "iOS";
      deviceId = deviceInfo['identifierForVendor'];
    }
    else if (Platform.isAndroid) {
      appPlatform = "Android";
      deviceId = deviceInfo['androidId'];
    }
    appInfo = AppInfo(
      app_platform: appPlatform,
      app_version: "1.0.0",
      device_id: deviceId,
    );
  }

  /// 登录
  void login(BuildContext context) async {
    print('手机号：${loginMobileController.text}');
    print('邮箱地址：${loginEmailController.text}');
    print('密码：${loginPasswordController.text}');
    print('验证码：${loginVerifyCodeController.text}');
    print('地区码：${regionCode.value}');
    print('登录方式：${loginAccountType.value}');
    print('验证方式：${loginPwdType.value}');

    /// 手机号登录
    if (loginAccountType.value == "MP") {
      if (!LoginValidator.validateMobile(loginMobileController.text, regionCode.value)) {
        return;
      }
    }
    else {
      if (!LoginValidator.validateEmail(loginEmailController.text)) {
        return;
      }
    }
    /// 账号密码登录
    if (loginPwdType.value == "Pwd") {
      if (!LoginValidator.validatePassword(loginPasswordController.text)) {
        return;
      }
    }
    /// 验证码登录
    else {
      if (!LoginValidator.validateCode(loginVerifyCodeController.text)) {
        return;
      }
    }

    if (!chooseBoxFlag.value) {
      HPLoading.showToast('login_agree_privacy_tips'.tr);
      return;
    }

    /// 账号密码登录
    if (loginPwdType.value == "Pwd") {
      print('账号密码登录');
      captCheckInfo = null;
      bool queryGt3Success = await queryGt3CaptchaInfo();
      if (queryGt3Success) {
        captchaType = "login";
      }
    }
    /// 验证码登录
    else {
      if (!hasSendCode) {
        HPLoading.showToast('login_send_code_tips'.tr);
        return;
      }
      loginRequest(captCheckInfo!);
    }
  }

  /// 获取验证码
  Future<void> sendVerifyCode() async {
    /// 邮箱验证码
    if (loginAccountType.value == "EMAIL") {
      if (!LoginValidator.validateEmail(loginEmailController.text)) {
        return;
      }
    }
    else {
      if (!LoginValidator.validateMobile(loginMobileController.text, regionCode.value)) {
        return;
      }
    }
    captCheckInfo = null;
    bool queryGt3Success = await queryGt3CaptchaInfo();
    if (queryGt3Success) {
      captchaType = "code";
      hasSendCode = true;
    }
    else {
      hasSendCode = false;
    }
  }

  /// 监听极验验证事件
  void onListenGt3Events() {
    Gt3CaptchaUtil.captcha.addEventHandler(
        onShow: (Map<String, dynamic> message) async {
          // TO-DO
          // 验证视图已展示 the captcha view is displayed
          print("Captcha did show");
        }, onClose: (Map<String, dynamic> message) async {
      // TO-DO
      // 验证视图已关闭 the captcha view is closed
      print("Captcha did close");
    }, onResult: (Map<String, dynamic> message) async {
      print("Captcha result: " + message.toString());
      String code = message["code"];
      if (code == "1") {
        // TO-DO
        // 发送 message["result"] 中的数据向 B 端的业务服务接口进行查询
        // 对结果进行二次校验 validate the result
        print("Captcha result result : ${message["result"]}");
        captCheckInfo = CaptCheckInfo(
          geetest_challenge: message["result"]["geetest_challenge"],
          geetest_seccode: message["result"]["geetest_seccode"],
          geetest_validate: message["result"]["geetest_validate"],
          capt_user_id: captchaResponse!.capt_user_id ?? "",
          category: captchaResponse!.category ?? "",
        );
        if (captchaType == "login") {
          handleLoginAction(captCheckInfo!);
        }
        else {
          /// 验证码
          handleCodeAction(captCheckInfo!);
        }
      } else {
        // 终端用户完成验证失败，自动重试 If the verification fails, it will be automatically retried.
        print("Captcha result code : " + code);
      }
    }, onError: (Map<String, dynamic> message) async {
      print("Captcha error: " + message.toString());
      String code = message["code"];

      // 处理验证中返回的错误 Handling errors returned in verification
      if (Platform.isAndroid) {
        // Android 平台
        if (code == "-2") {
          // Dart 调用异常 Call exception
        } else if (code == "-1") {
          // Gt3RegisterData 参数不合法 Parameter is invalid
        } else if (code == "201") {
          // 网络无法访问 Network inaccessible
        } else if (code == "202") {
          // Json 解析错误 Analysis error
        } else if (code == "204") {
          // WebView 加载超时，请检查是否混淆极验 SDK   Load timed out
        } else if (code == "204_1") {
          // WebView 加载前端页面错误，请查看日志 Error loading front-end page, please check the log
        } else if (code == "204_2") {
          // WebView 加载 SSLError
        } else if (code == "206") {
          // gettype 接口错误或返回为 null   API error or return null
        } else if (code == "207") {
          // getphp 接口错误或返回为 null    API error or return null
        } else if (code == "208") {
          // ajax 接口错误或返回为 null      API error or return null
        } else {
          // 更多错误码参考开发文档  More error codes refer to the development document
          // https://docs.geetest.com/sensebot/apirefer/errorcode/android
        }
      }

      if (Platform.isIOS) {
        // iOS 平台
        if (code == "-1009") {
          // 网络无法访问 Network inaccessible
        } else if (code == "-1004") {
          // 无法查找到 HOST  Unable to find HOST
        } else if (code == "-1002") {
          // 非法的 URL  Illegal URL
        } else if (code == "-1001") {
          // 网络超时 Network timeout
        } else if (code == "-999") {
          // 请求被意外中断, 一般由用户进行取消操作导致 The interrupted request was usually caused by the user cancelling the operation
        } else if (code == "-21") {
          // 使用了重复的 challenge   Duplicate challenges are used
          // 检查获取 challenge 是否进行了缓存  Check if the fetch challenge is cached
        } else if (code == "-20") {
          // 尝试过多, 重新引导用户触发验证即可 Try too many times, lead the user to request verification again
        } else if (code == "-10") {
          // 预判断时被封禁, 不会再进行图形验证 Banned during pre-judgment, and no more image captcha verification
        } else if (code == "-2") {
          // Dart 调用异常 Call exception
        } else if (code == "-1") {
          // Gt3RegisterData 参数不合法  Parameter is invalid
        } else {
          // 更多错误码参考开发文档 More error codes refer to the development document
          // https://docs.geetest.com/sensebot/apirefer/errorcode/ios
        }
      }


    });
  }

  /// 获取极验图形验证码信息
  Future<bool> queryGt3CaptchaInfo() async {
    HPResponse response = await apiRepository.getCaptcha();
    if (response.isSuccess) {
      captchaResponse = Gt3CaptchaResponse.fromJson(response.data);
      Gt3CaptchaUtil.startCaptcha(
          captchaResponse!.gt ?? "", captchaResponse!.challenge ?? "");
      return true;
    }
    HPLoading.showError(response.msg);
    return false;
  }

  /// 处理登录
  Future<void> handleLoginAction(CaptCheckInfo captCheckInfo) async {
    await loginRequest(captCheckInfo);
  }

  /// 登录请求
  Future<void> loginRequest(CaptCheckInfo captCheckInfo) async {
    print('开始登录。。。。。。');
    late LoginRequest loginRequest;
    /// 手机号+密码
    if (loginPwdType.value == "Pwd" && loginAccountType.value == "MP") {
      String passwordStr = await HPEncrypt.getSha256String(loginPasswordController.text);
      loginRequest = LoginRequest(
        login_account_type: loginAccountType.value,
        mp_area_code: regionCode.value,
        mp: loginMobileController.text,
        password: passwordStr,
        capt_check_info: captCheckInfo,
        app_info: appInfo,
      );
    }
    /// 手机号+验证码
    else if (loginPwdType.value == "Code" && loginAccountType.value == "MP") {
      loginRequest = LoginRequest(
        login_account_type: loginAccountType.value,
        mp_area_code: regionCode.value,
        mp: loginMobileController.text,
        captcha: loginVerifyCodeController.text,
        capt_check_info: captCheckInfo,
        app_info: appInfo,
      );
    }
    /// 邮箱+密码
    else if (loginPwdType.value == "Pwd" && loginAccountType.value == "EMAIL") {
      String passwordStr = await HPEncrypt.getSha256String(loginPasswordController.text);
      loginRequest = LoginRequest(
        login_account_type: loginAccountType.value,
        email: loginEmailController.text,
        password: passwordStr,
        capt_check_info: captCheckInfo,
        app_info: appInfo,
      );
    }
    /// 邮箱+验证码
    else if (loginPwdType.value == "Code" && loginAccountType.value == "EMAIL") {
      loginRequest = LoginRequest(
        login_account_type: loginAccountType.value,
        email: loginEmailController.text,
        captcha: loginVerifyCodeController.text,
        capt_check_info: captCheckInfo,
        app_info: appInfo,
      );
    }
    HPResponse loginResponse = await apiRepository.login(loginRequest.toJson());
    if (loginResponse.isSuccess) {
      final res = LoginResponse.fromJson(loginResponse.data);
      StorageService.setString(StorageConstants.token, res.hf_token);
      if (res.count == 1) {
        HPResponse merchantListResponse = await apiRepository.queryMerchantList();
        if (merchantListResponse.isSuccess) {
          SubUserInfoList subUserInfoList = SubUserInfoList.fromJson(merchantListResponse.data);
          if (subUserInfoList.sub_user_info_list != null && subUserInfoList.sub_user_info_list!.isNotEmpty) {
            SubUserInfo subUserInfo = subUserInfoList.sub_user_info_list!.first;
            Map<String, dynamic> data = {
              "sub_user_id": subUserInfo.sub_user_id,
              "app_info": appInfo.toJson(),
            };
            HPResponse chooseMerchantResponse = await apiRepository.chooseMerchant(data);
            if (chooseMerchantResponse.isSuccess) {
              final SwitchData switchData = SwitchData.fromJson(chooseMerchantResponse.data);
              /// 保存登录状态
              StorageService.setBool(StorageConstants.loginFlag, true);
              /// 保存hfToken
              StorageService.setString(StorageConstants.token, switchData.hfToken!);
              /// 保存subUserId
              StorageService.setString(
                  StorageConstants.userInfo, json.encode(subUserInfo.toJson()));
              HPLoading.showSuccess('登录成功');
              HPRouter.resolvePage(Routes.main);
            }
            else {
              StorageService.setBool(StorageConstants.loginFlag, false);
              HPLoading.showError(chooseMerchantResponse.msg);
            }
          }
        }
      }
      /// 多商户进入商户选择页
      else if (res.count > 1) {
        HPRouter.pushPage(Routes.merchantSelect);
      }
    }
    else {
      HPLoading.showError(loginResponse.msg);
    }
  }

  /// 处理获取验证码
  Future<void> handleCodeAction(CaptCheckInfo captCheckInfo) async {
    LoginSendVerifyCodeRequest loginSendVerifyCodeRequest;
    if (loginAccountType.value == "EMAIL") {
      loginSendVerifyCodeRequest = LoginSendVerifyCodeRequest(
        login_account_type: loginAccountType.value,
        email: loginEmailController.text,
        capt_check_info: captCheckInfo,
        captcha_type: "1",
      );
    }
    else {
      loginSendVerifyCodeRequest = LoginSendVerifyCodeRequest(
        login_account_type: loginAccountType.value,
        mp_area_code: regionCode.value,
        mp: loginMobileController.text,
        capt_check_info: captCheckInfo,
        captcha_type: "1",
      );
    }
    HPResponse sendVerifyCodeResponse = await apiRepository.sendVerifyCode(loginSendVerifyCodeRequest.toJson());
    if (sendVerifyCodeResponse.isSuccess) {
      _startCountdown();
      HPLoading.showSuccess('login_send_code_success_tips'.tr);
    }
    else {
      HPLoading.showError(sendVerifyCodeResponse.msg);
    }
  }

  void _startCountdown() {
    print('开始倒计时...');

    isCounting.value = true;
    seconds = 60;

    // 设置周期性定时器（每秒执行一次）
    timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (seconds > 0) {
        print('倒计时剩余时间：${seconds}s');
        verifyCodeBtnTitle.value = 'login_resend_code_title'.tr + ' ${seconds}s';
        seconds--; // 秒数递减
      } else {
        timer?.cancel();  // 倒计时结束取消定时器
        isCounting.value = false; // 重置状态
        verifyCodeBtnTitle.value = 'login_resend_code_title'.tr;
      }
    });
  }

  /// 切换登录方式
  void changeLoginMethods() {
    loginAccountType.value = loginAccountType.value == 'MP' ? 'EMAIL' : 'MP';
    timer?.cancel();  // 倒计时结束取消定时器
    isCounting.value = false; // 重置状态
    verifyCodeBtnTitle.value = 'login_send_code_title'.tr;
  }

  @override
  void onClose() {
    timer?.cancel(); // 组件销毁时取消定时器
    // TODO: implement onClose
    super.onClose();
  }
}
