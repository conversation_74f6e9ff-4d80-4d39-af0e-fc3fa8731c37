/*
 * ProjectName：UaApp
 * FilePath：lib/pages/login/merchantSelect
 * FileName：merchant_select_controller
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/16 18:41:36
 */
import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/base/base_controller.dart';
import 'package:auapp/models/HPUserInfoSingleton.dart';
import 'package:auapp/pages/merchantSelect/models/sub_user_info.dart';
import 'package:auapp/pages/merchantSelect/models/switch_data.dart';
import 'package:auapp/services/storage_service.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:get/get.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/pages/merchantSelect/models/merchant_select_data.dart';
import '../../pub/constants/storage_constants.dart';
import 'package:auapp/services/app_info_service.dart';

class MerchantSelectController extends BaseController {
  // 使用 RxList 监听列表变化
  final RxList<SubUserInfo> merchantList = <SubUserInfo>[].obs;
  // 加载状态，默认为 true，数据加载完毕后设置为 false
  final RxBool isLoading = true.obs;

  final bool autoLoad; // 是否自动加载
  MerchantSelectController({this.autoLoad = true});

  @override
  void onInit() {
    super.onInit();
    if (autoLoad) {
      // 初始化时获取商户数据
      getUserSwitchList();
    }
  }

  /// 获取子用户列表
  Future<void> getUserSwitchList() async {
    try {
      isLoading.value = true;
      HPResponse response =
          await HttpRequest.get(ApiConstants.getUserSwitchListUrl);
      if (response.isSuccess) {
        final res = MerchantSelectListData.fromJson(response.data);
        final List<SubUserInfo> subSelectList = res.subUserInfoList ?? [];
        if (subSelectList.isNotEmpty) {
          merchantList.assignAll(subSelectList);
        }
      } else {
        HPLoading.showToast(response.msg);
      }
    } catch (e) {
      HPLoading.showToast(e.toString());
    } finally {
      isLoading.value = false;
    }
  }

  /// 切换子用户
  Future<bool> switchUser(SubUserInfo subUserInfo) async {
    try {
      // 正确方式：从GetX获取已初始化的AppInfoService实例
      final AppInfoService appInfoService = Get.find<AppInfoService>();

      // 使用 AppInfoService 提供的方法获取 app_info 的 JSON 表示
      Map<String, dynamic> appInfoJson = appInfoService.getAppInfoJson();

      HPResponse response =
          await HttpRequest.post(ApiConstants.switchUserUrl, params: {
        'sub_user_id': subUserInfo.subUserId,
        'app_info': appInfoJson // 使用从服务获取的 appInfoJson
      });
      if (response.isSuccess) {
        final SwitchData switchData = SwitchData.fromJson(response.data);
        if (switchData.hfToken != null) {
          // 保存hfToken
          StorageService.setString(StorageConstants.token, switchData.hfToken!);

          /// 保存登录状态
          await StorageService.setBool(StorageConstants.loginFlag, true);

          await HPUserInfoSingleton().updateUserInfo(subUserInfo: subUserInfo);
          // 跳转首页
          HPRouter.resolvePage(Routes.main,
              preventDuplicates: false, destroyBinding: true);

          return true;
        }
      } else {
        StorageService.setBool(StorageConstants.loginFlag, false);
        HPLoading.showToast(response.msg);
      }
    } catch (e) {
      HPLoading.showToast(e.toString());
    }

    return false;
  }
}
