import 'sub_user_info.dart';

class MerchantSelectListData {
  List<SubUserInfo>? subUserInfoList;

  MerchantSelectListData({this.subUserInfoList});

  MerchantSelectListData.fromJson(Map<String, dynamic> json) {
    if (json['sub_user_info_list'] != null) {
      subUserInfoList = <SubUserInfo>[];
      json['sub_user_info_list'].forEach((v) {
        subUserInfoList!.add(new SubUserInfo.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.subUserInfoList != null) {
      data['sub_user_info_list'] =
          this.subUserInfoList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
