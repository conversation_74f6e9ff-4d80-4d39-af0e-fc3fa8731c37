/*
 * ProjectName：UaApp
 * FilePath：lib/pages/merchantSelect/models/sub_user_info.dart
 * FileName：sub_user_info
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description SubUserInfo model
 * @date 2025/5/17
 */

class SubUserInfo {
  String? subUserId;
  String? status;
  String? isEditPwd;
  String? orgId;
  String? custId;
  String? custRegName;
  String? custEnName;
  String? custShortName;
  String? custStatus;

  SubUserInfo(
      {this.subUserId,
      this.status,
      this.isEditPwd,
      this.orgId,
      this.custId,
      this.custRegName,
      this.custEnName,
      this.custShortName,
      this.custStatus});

  factory SubUserInfo.fromJson(Map<String, dynamic> json) {
    return SubUserInfo(
      subUserId: json['sub_user_id'],
      status: json['status'],
      isEditPwd: json['is_edit_pwd'],
      orgId: json['org_id'],
      custId: json['cust_id'],
      custRegName: json['cust_reg_name'],
      custEnName: json['cust_en_name'],
      custShortName: json['cust_short_name'],
      custStatus: json['cust_status'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['sub_user_id'] = this.subUserId;
    data['status'] = this.status;
    data['is_edit_pwd'] = this.isEditPwd;
    data['org_id'] = this.orgId;
    data['cust_id'] = this.custId;
    data['cust_reg_name'] = this.custRegName;
    data['cust_en_name'] = this.custEnName;
    data['cust_short_name'] = this.custShortName;
    data['cust_status'] = this.custStatus;
    return data;
  }
}
