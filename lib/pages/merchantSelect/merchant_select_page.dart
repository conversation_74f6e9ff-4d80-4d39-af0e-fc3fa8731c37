/*
 * ProjectName：UaApp
 * FilePath：lib/pages/login/merchantSelect
 * FileName：merchant_select_page
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/16 18:40:49
 */

import 'package:auapp/pages/merchantSelect/merchant_select_controller.dart';
import 'package:auapp/widgets/hp_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../r.dart';
import 'models/sub_user_info.dart';

class MerchantSelectPage extends GetView<MerchantSelectController> {
  const MerchantSelectPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const HPAppBar(
        text: '',
        backgroundColor: Colors.transparent, // 透明背景以显示渐变
      ),
      extendBodyBehindAppBar: true, // 使 body 内容延伸到 AppBar 后方
      body: Container(
        // 背景图片，全屏平铺
        decoration: BoxDecoration(
          color: Colors.white,
          image: DecorationImage(
            image: AssetImage(R.assetsImagesSelectMerchantBg),
            fit: BoxFit.cover, // 全屏平铺，可能会裁剪图片以适应
          ),
        ),
        child: SafeArea(
          // 确保内容不会被系统 UI遮挡 (例如状态栏)
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.sp),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 28.sp), // 为 AppBar 留出空间
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0), // 调整大标题与副标题的间距
                  child: Text(
                    "choose_title".tr,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 24.sp, // 大标题字号
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF222527),
                    ),
                  ),
                ),
                Padding(
                  padding:
                      EdgeInsets.only(top: 8.sp, bottom: 32.sp), // 调整副标题与列表的距离
                  child: Text(
                    "choose_sub_title".tr,
                    style: TextStyle(
                        fontSize: 12.sp, color: const Color(0xFF6B7275)),
                  ),
                ),
                Expanded(
                  child: Obx(() {
                    // 使用 Obx 监听 controller 中的可观察变量
                    if (controller.isLoading.value) {
                      return const Center(child: CircularProgressIndicator());
                    }
                    if (controller.merchantList.isEmpty) {
                      return const Center(child: Text(''));
                    }
                    return ListView.builder(
                      padding: EdgeInsets.zero, // 去除 ListView 的默认 padding
                      itemCount: controller.merchantList.length,
                      itemBuilder: (context, index) {
                        final SubUserInfo merchant =
                            controller.merchantList[index];
                        return _buildMerchantItem(context, merchant);
                      },
                    );
                  }),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMerchantItem(BuildContext context, SubUserInfo merchant) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      color: Colors.white, // 明确卡片背景为白色
      elevation: 3.0, // 轻微的阴影效果
      child: InkWell(
        onTap: () {
          controller.switchUser(merchant);
        },
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: const EdgeInsets.symmetric(
              horizontal: 16.0, vertical: 20.0), // 调整内边距
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      merchant.custShortName ?? '',
                      style: const TextStyle(
                        fontSize: 17.0, // 稍微调整字体大小
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 6.0), // 调整间距
                    Text(
                      merchant.custRegName ?? '',
                      style: TextStyle(
                        fontSize: 13.0, // 稍微调整字体大小
                        color: Colors.grey[600],
                      ),
                      maxLines: 1, // 最多显示一行
                      overflow: TextOverflow.ellipsis, // 超出部分显示省略号
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16.0),
              Icon(
                Icons.arrow_forward_ios,
                size: 16.0,
                color: Colors.grey[500],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
