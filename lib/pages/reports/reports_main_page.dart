import 'dart:convert';

import 'package:auapp/models/HPUserInfoSingleton.dart';
import 'package:auapp/pages/main/models/main_login_operator_info.dart';
import 'package:auapp/pages/mine/mine_main_controller.dart';
import 'package:auapp/pub/constants/color_constants.dart';
import 'package:auapp/pub/constants/storage_constants.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/services/storage_service.dart';
import 'package:auapp/widgets/hp_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../widgets/hp_border_button.dart';
import '../login/widgets/login_app_bar.dart';

class ReportsMainPage extends GetView<MineMainController> {
  const ReportsMainPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: LoginAppBar.appBar(
      context,
      'main_tab_title_reports'.tr,
      null,
      Colors.transparent,
    ),
      body: Center(child: Column(
        children: [
          Text('main_tab_title_reports'.tr),
          HPBorderButton(
            text: 'transaction_detail_title'.tr,
            backgroundColor: ColorConstants.mainThemeColor,
            onPressed: () {
              String loginOperatorInfo = StorageService.getString(StorageConstants.loginOperatorInfo) ?? "";
              // if (loginOperatorInfo.isNotEmpty) {
              //   MainLoginOperatorInfo mainLoginOperatorInfo = MainLoginOperatorInfo.fromJson(json.decode(loginOperatorInfo));
              //   print("mainLoginOperatorInfo: ${mainLoginOperatorInfo.org_cust_id}");
              // }
              print("=== ------ : ${HPUserInfoSingleton().memberCode}");
              HPRouter.pushPage(Routes.transactionRefund, parameters: {
                "transactionId": "123456",
                "remainAmount": "10.98",
                "orderCurrency": "AUD",
                "orderAmount": "124.50",
              });
            },
          )
        ],
      ),),
    );
  }
}