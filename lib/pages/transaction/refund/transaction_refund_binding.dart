/*
 * ProjectName：UaApp
 * FilePath：lib/pages/transaction/refund
 * FileName：transaction_refund_binding
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/25 16:17:16
 */

import 'package:auapp/base/base_binding.dart';
import 'package:auapp/pages/transaction/refund/transaction_refund_controller.dart';
import 'package:auapp/pages/transaction/refund/transaction_refund_provider.dart';
import 'package:get/get.dart';

class TransactionRefundBinding extends BaseBinding {
  @override
  void dependencies() {
    Get.lazyPut(() => TransactionRefundController(transactionRefundProvider: Get.find()));
    Get.lazyPut(() => TransactionRefundProvider());
  }

}