/*
 * ProjectName：UaApp
 * FilePath：lib/pages/transaction/refund
 * FileName：transaction_refund_page
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/25 16:16:41
 */

import 'package:auapp/pages/transaction/detail/widgets/transaction_detail_item_cell.dart';
import 'package:auapp/pages/transaction/refund/transaction_refund_controller.dart';
import 'package:auapp/pub/constants/color_constants.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/utils/app_focus.dart';
import 'package:auapp/utils/hp_input_formater.dart';
import 'package:auapp/utils/hp_money_input_format.dart';
import 'package:auapp/widgets/hp_app_bar.dart';
import 'package:auapp/widgets/hp_border_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

import '../../../r.dart';

class TransactionRefundPage extends GetView<TransactionRefundController> {
  const TransactionRefundPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: HPAppBar(
        text: 'transaction_detail_refund'.tr,
        backgroundColor: Colors.white,
      ),
      backgroundColor: ThemeConfig.grayMainBackgroundColor,
      resizeToAvoidBottomInset: false, // 默认值为 true，自动调整布局
      body: GestureDetector(
        onTap: () {
          AppFocus.unfocus(context);
        },
        child: Column(
          children: [
            Expanded(
              child: Column(
                children: [
                  _buildRefundInfo(),
                  _buildOriginalOrderInfo(context),
                ],
              ),
            ),
            _buildBottom(context),
          ],
        ),
      ),
    );
  }

  Widget _buildRefundInfo() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(16.sp),
          bottomRight: Radius.circular(16.sp),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 17.sp,
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.sp),
            child: Text(
              'transaction_refund_amount_lb'.tr,
              style: TextStyle(
                fontSize: 15.sp,
                fontWeight: FontWeight.w500,
                color: ColorConstants.darkTextColor,
              ),
            ),
          ),
          SizedBox(
            height: 24.sp,
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 210.sp,
                  child: CupertinoTextField(
                    controller: controller.refundAmountInputController,
                    maxLines: 1,
                    enabled: true,
                    keyboardType:
                        const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [
                      HPInputFormatter.inputMoneyFormatter,
                      HpMoneyInputFormat(),
                    ],
                    style: TextStyle(
                      color: const Color(0xFF222527),
                      fontSize: 28.sp,
                      fontFamily: "DingTalk-JinBuTi",
                    ),
                    textAlign: TextAlign.left,
                    onChanged: (text) {
                      controller.showDel.value = text.isNotEmpty;
                    },
                    placeholder: "transaction_refund_input_placeholder".tr,
                    placeholderStyle: TextStyle(
                      fontSize: 28.sp,
                      color: const Color(0xFFC9CDD4),
                    ),
                    decoration: null,
                    autocorrect: false,
                  ),
                ),
                Row(
                  children: [
                    Obx(() => Visibility(
                          visible: controller.showDel.value,
                          child: GestureDetector(
                            onTap: () {
                              controller.refundAmountInputController.clear();
                              controller.showDel.value = false;
                            },
                            child: Image.asset(
                              R.assetsImagesLoginIconDel,
                              width: 24.sp,
                              height: 24.sp,
                            ),
                          ),
                        )),
                    SizedBox(width: 8.sp),
                    GestureDetector(
                      onTap: () {
                        controller.refundAmountInputController.text =
                            controller.remainAmount.toString();
                        controller.showDel.value = true;
                      },
                      child: Text(
                        "transaction_refund_all_btn".tr,
                        style: TextStyle(
                            fontSize: 16.sp,
                            color: ColorConstants.mainThemeColor),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 8.sp, horizontal: 24.sp),
            child: Divider(
              color: const Color(0xFFE5E6EB),
              height: 0.5.sp,
            ),
          ),
          TransactionDetailItemCell(
            leftTitle: 'transaction_refundable_amount_lb'.tr,
            rightContent:
                "${controller.orderCurrency} ${controller.remainAmount}",
          ),
          SizedBox(height: 18.sp),
        ],
      ),
    );
  }

  Widget _buildOriginalOrderInfo(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 8.sp),
      padding: EdgeInsets.symmetric(vertical: 18.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(16.sp)),
      ),
      child: Column(
        children: [
          TransactionDetailItemCell(
            leftTitle: 'transaction_huepay_order_id_lb'.tr,
            rightContent: controller.transactionId,
          ),
          TransactionDetailItemCell(
            leftTitle: 'transaction_detail_order_sale_amt_lb'.tr,
            rightContent:
                "${controller.orderCurrency} ${controller.orderAmount}",
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 24.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  alignment: Alignment.topLeft,
                  child: Text(
                    'transaction_refund_remark_lb'.tr,
                    softWrap: true, // 启用换行
                    style: TextStyle(
                      fontSize: 13.0.sp,
                      color: const Color(0xFF6B7275),
                    ),
                  ),
                ),
                SizedBox(width: 10.sp),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Container(
                          alignment: Alignment.centerRight,
                          child: Obx(
                            () => CupertinoTextField(
                              padding: EdgeInsets.symmetric(vertical: 6.sp),
                              controller:
                                  controller.refundRemarkInputController,
                              maxLines: 1,
                              maxLength: 128,
                              enabled:
                                  controller.refundRemarkBtnState.value == "2",
                              keyboardType: TextInputType.text,
                              style: TextStyle(
                                color: ThemeConfig.darkTextColor,
                                fontSize: 13.sp,
                                fontWeight: FontWeight.normal,
                              ),
                              textAlign: TextAlign.right,
                              onChanged: (text) {
                                // controller.refundRemark.value = text;
                              },
                              placeholder:
                                  controller.refundRemarkBtnState.value == "2"
                                      ? "transaction_refund_remark_placeholder"
                                          .tr
                                      : "",
                              placeholderStyle: TextStyle(
                                fontSize: 13.sp,
                                color: const Color(0xFFC9CDD4),
                                fontWeight: FontWeight.normal,
                              ),
                              decoration: const BoxDecoration(
                                color: Colors.white,
                              ),
                              autocorrect: false,
                            ),
                          ),
                        ),
                      ),
                      Obx(() => GestureDetector(
                            onTap: () {
                              /// 添加
                              if (controller.refundRemarkBtnState.value ==
                                  "1") {
                                controller.showRemarkInput.value = true;
                                controller.refundRemarkBtnState.value = "2";
                              }

                              /// 保存
                              else if (controller.refundRemarkBtnState.value ==
                                  "2") {
                                controller.showRemarkInput.value = false;
                                controller.refundRemarkBtnState.value = "3";
                                controller.refundRemark.value =
                                    controller.refundRemarkInputController.text;
                              }

                              /// 编辑
                              else {
                                controller.showRemarkInput.value = true;
                                controller.refundRemarkBtnState.value = "2";
                              }
                            },
                            child: Padding(
                              padding: EdgeInsets.only(left: 5.sp),
                              child: Text(
                                controller.refundRemarkBtnState.value == "1"
                                    ? "transaction_refund_add_remark_lb".tr
                                    : (controller.refundRemarkBtnState.value ==
                                            "2"
                                        ? "transaction_refund_save_remark_lb".tr
                                        : "transaction_refund_edit_remark_lb"
                                            .tr),
                                style: TextStyle(
                                  fontSize: 13.0.sp,
                                  color: ColorConstants.mainThemeColor,
                                ),
                              ),
                            ),
                          )),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottom(BuildContext context) {
    return Container(
      color: Colors.white,
      child: SafeArea(
        // 处理安全区域（如刘海屏）
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 24.sp, vertical: 12.sp),
          alignment: Alignment.center,
          child: SizedBox(
            width: 1.sw,
            child: HPBorderButton(
                text: 'transaction_refund_confirm_btn'.tr,
                onPressed: () {
                  controller.onConfirm(context);
                }),
          ),
        ),
      ),
    );
  }
}
