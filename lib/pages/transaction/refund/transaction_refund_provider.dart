/*
 * ProjectName：UaApp
 * FilePath：lib/pages/transaction/refund
 * FileName：transaction_refund_provider
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/25 16:17:38
 */

import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';

class TransactionRefundProvider {
  /// 交易退款详情接口
  Future<HPResponse> refund(String transactionId, String amount, String password, String? remark) async {
    HPResponse response = await HttpRequest.post(
        ApiConstants.transactionRefundUrl,
        customBaseUrl: ApiConstants.baseTransactionUrl,
        params: {
          "originalTransactionId": transactionId,
          "amount": amount,
          "password": password,
          "remark": remark
        }
    );
    return response;
  }
}
