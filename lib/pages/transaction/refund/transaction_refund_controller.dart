/*
 * ProjectName：UaApp
 * FilePath：lib/pages/transaction/refund
 * FileName：transaction_refund_controller
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/25 16:17:00
 */

import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/pages/transaction/refund/transaction_refund_provider.dart';
import 'package:auapp/pub/constants/color_constants.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/utils/hp_encrypt.dart';
import 'package:auapp/utils/hp_input_formater.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:auapp/widgets/hp_border_button.dart';
import 'package:auapp/widgets/hp_dialog.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../r.dart';

class TransactionRefundController extends GetxController {
  final TransactionRefundProvider transactionRefundProvider;
  TransactionRefundController({required this.transactionRefundProvider});
  final refundAmountInputController = TextEditingController();
  final refundRemarkInputController = TextEditingController();
  late String? transactionId;
  late String? remainAmount;
  late String? orderCurrency;
  late String? orderAmount;

  final showDel = false.obs; // 是否显示删除
  final refundRemark = "".obs;

  /// 退款备注
  final showRemarkInput = false.obs; // 是否显示退款备注输入框
  final refundRemarkBtnState = "1".obs;

  /// 1添加备注 2保存备注 3编辑备注

  late OverlayEntry overlayEntry;

  final TextEditingController passwordInputController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();

    transactionId = Get.parameters['transactionId']!;
    remainAmount = Get.parameters['remainAmount']!;
    orderCurrency = Get.parameters['orderCurrency']!;
    orderAmount = Get.parameters['orderAmount']!;
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
  }

  /// 确认
  void onConfirm(BuildContext context) {
    print("refundAmount:${refundAmountInputController.text}");
    print("refundRemark:${refundRemarkInputController.text}");
    print("refundRemark:${refundRemark.value}");

    /// 退款权限校验

    /// 退款金额校验
    if (refundAmountInputController.text.isEmpty ||
        double.parse(refundAmountInputController.text) <= 0) {
      HPLoading.showError('transaction_refund_input_placeholder'.tr);
      return;
    }

    /// 退款金额不能大于剩余可退金额
    if (double.parse(refundAmountInputController.text) >
        double.parse(remainAmount!)) {
      HPLoading.showError('退款金额不能超过可退金额');
      return;
    }

    _showInputWithKeyboard(context);

    // refund();
  }

  /// 退款
  Future<void> refund() async {
    HPResponse response = await transactionRefundProvider.refund(
        transactionId!,
        refundAmountInputController.text,
        passwordInputController.text,
        refundRemarkInputController.text);
    if (response.isSuccess) {

    }
  }

  void _showInputWithKeyboard(BuildContext context) {
    // 创建 OverlayEntry
    overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // 遮罩层
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                // 点击遮罩层时关闭键盘和输入框
                // _focusNode.unfocus();
                // overlayEntry.remove();
              },
              behavior: HitTestBehavior.opaque, // 拦截所有触摸事件
              child: Container(
                color: const Color(0x99000000), // 半透明黑色遮罩
              ),
            ),
          ),
          // 输入框区域
          Positioned(
            bottom: MediaQuery.of(context).viewInsets.bottom,
            left: 0,
            right: 0,
            child: Material(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.sp),
                  topRight: Radius.circular(16.sp),
                ),
              ),
              child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: 16.sp, vertical: 14.sp),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16.sp),
                        topRight: Radius.circular(16.sp),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          width: 24.sp,
                        ),
                        Text(
                          'transaction_refund_password_popup_title'.tr,
                          style: TextStyle(
                              fontSize: 15.sp,
                              fontWeight: FontWeight.w500,
                              color: ThemeConfig.darkTextColor),
                        ),
                        GestureDetector(
                          onTap: () {
                            _focusNode.unfocus();
                            overlayEntry.remove();
                          },
                          child: Image.asset(
                            R.assetsImagesLoginPopupClose,
                            width: 24.sp,
                            height: 24.sp,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.all(24.sp),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(9.sp),
                    ),
                    child: TextField(
                      controller: passwordInputController,
                      focusNode: _focusNode,
                      autofocus: true,
                      obscureText: true, // 启用星号显示
                      obscuringCharacter: '*', // 指定星号字符
                      textAlign: TextAlign.center, // 文本对齐方式,
                      style: TextStyle(
                        color: ThemeConfig.darkTextColor,
                        fontSize: 20.sp,
                        fontWeight: FontWeight.normal,
                      ),
                      inputFormatters: [
                        HPInputFormatter.denyChineseAndEmojiFormatter,
                      ],
                      decoration: InputDecoration(
                        hintText:
                            "transaction_refund_password_popup_input_placeholder"
                                .tr,
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                            vertical: 15.sp, horizontal: 15.sp),
                        fillColor: Colors.transparent,
                        hintStyle: TextStyle(
                          fontSize: 20.sp,
                          color: const Color(0xFFC9CDD4),
                          fontWeight: FontWeight.normal,
                        ),
                        floatingLabelBehavior: FloatingLabelBehavior.always,
                        filled: true,
                        isDense: true,
                      ),
                    ),
                  ),
                  Container(
                    width: 1.sw,
                    padding: EdgeInsets.only(
                        left: 24.sp, right: 24.sp, bottom: 30.sp),
                    child: HPBorderButton(
                      text: 'transaction_refund_confirm_btn'.tr,
                      onPressed: () {
                        _focusNode.unfocus();
                        overlayEntry.remove();
                        HPDialog.show(
                          context,
                          title:
                              'transaction_refund_twice_confirm_dialog_title'
                                  .tr,
                          content:
                              'transaction_refund_twice_confirm_dialog_content_start'
                                      .tr +
                                  refundAmountInputController.text +
                                  'transaction_refund_twice_confirm_dialog_content_end'
                                      .tr,
                          onConfirmPress: () {
                            refund();
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );

    // 显示Overlay
    Overlay.of(context).insert(overlayEntry);
    // 自动弹出键盘
    _focusNode.requestFocus();
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
  }
}
