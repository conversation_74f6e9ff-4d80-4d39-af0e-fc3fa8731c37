/*
 * ProjectName：UaApp
 * FilePath：lib/pages/transaction/refundResult/models
 * FileName：refund_result_model
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/27 09:29:45
 */

import 'package:json_annotation/json_annotation.dart';
part 'refund_result_model.g.dart';

@JsonSerializable()
class RefundResultModel {
  /// Huepay订单号
  String? transactionId;

  /// 订单状态(枚举值SUCCEED成功，PENDING 处理中, FAILED失败)
  String? transactionResult;

  /// 币种
  String? orderCurrency;

  /// 订单金额
  num? orderAmount;

  /// 交易时间（北京时间）
  String? orderTime;

  /// 备注
  String? descriptor;

  RefundResultModel({
    this.transactionId,
    this.transactionResult,
    this.orderCurrency,
    this.orderAmount,
    this.orderTime,
    this.descriptor,
  });

  factory RefundResultModel.fromJson(Map<String, dynamic> json) => _$RefundResultModelFromJson(json);
  Map<String, dynamic> toJson() => _$RefundResultModelToJson(this);
}