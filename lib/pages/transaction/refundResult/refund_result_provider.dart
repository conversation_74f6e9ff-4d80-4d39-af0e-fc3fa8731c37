/*
 * ProjectName：UaApp
 * FilePath：lib/pages/transaction/refundResult
 * FileName：refund_result_provider
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/27 09:16:07
 */

import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';

class RefundResultProvider {
  /// 查询退款状态接口
  Future<HPResponse> fetchTransactionRefundStatusDetail(String transactionId) async {
    HPResponse response = await HttpRequest.post(
        ApiConstants.transactionRefundResultInfoUrl,
        customBaseUrl: ApiConstants.baseTransactionUrl,
        params: {
          "transactionId": transactionId,
        }
    );
    return response;
  }
}
