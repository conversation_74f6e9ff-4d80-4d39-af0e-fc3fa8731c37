/*
 * ProjectName：UaApp
 * FilePath：lib/pages/transaction/refundResult
 * FileName：refund_result_binding
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/27 09:15:57
 */
import 'package:auapp/base/base_binding.dart';
import 'package:auapp/pages/transaction/refundResult/refund_result_controller.dart';
import 'package:auapp/pages/transaction/refundResult/refund_result_provider.dart';
import 'package:get/get.dart';


class RefundResultBinding implements BaseBinding {
  @override
  void dependencies() {
    Get.lazyPut<RefundResultController>(() => RefundResultController(refundResultProvider: Get.find()));
    Get.lazyPut(() => RefundResultProvider());
  }
}
