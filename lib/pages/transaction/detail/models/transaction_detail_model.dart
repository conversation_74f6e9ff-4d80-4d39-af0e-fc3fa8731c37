/*
 * ProjectName：UaApp
 * FilePath：lib/pages/transaction/detail/models
 * FileName：transaction_detail_model
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/23 16:32:08
 */

import 'package:json_annotation/json_annotation.dart';
part 'transaction_detail_model.g.dart';

@JsonSerializable()
class TransactionDetailModel {
  /// 商户名称
  final String? clientName;

  /// Huepay订单号
  final String? transactionId;

  /// 外部交易单号
  final String? authTraceNo;

  /// 订单状态（枚举值SUCCEED成功，PENDING 处理中, FAILED失败）
  final String? transactionResult;

  /// 订单类型（SALE REFUND）
  final String? transactionType;

  /// 币种
  final String? orderCurrency;

  /// 订单金额
  final num? orderAmount;

  /// 总金额
  final num? paymentAmount;

  /// 手续费
  final num? feeAmount;

  /// 结算金额
  final num? settleAmount;

  /// 可退金额
  final num? remainAmount;

  /// 消费者承担手续费
  final num? customerChargeFee;

  /// 支付方式
  final String? paymentMethod;

  /// 本地时间
  final String? timezoneOrderTime;

  /// 交易时间（北京时间）东8区时间
  final String? orderTime;

  /// 备注
  final String? descriptor;

  /// Customerid （openid/buyerid）
  final String? merchantCustomerId;

  /// 退款列表（交易类型正向交易才会有 即  transactionType = SALE）
  final List<TransactionRefundInfo>? refundList;

  /// 交易类型反向交易才会有 即  transactionType = Refund
  final TransactionOriginalTransInfo? orgTransInfo;

  TransactionDetailModel({
    this.clientName,
    this.transactionId,
    this.authTraceNo,
    this.transactionResult,
    this.transactionType,
    this.orderCurrency,
    this.orderAmount,
    this.paymentAmount,
    this.feeAmount,
    this.settleAmount,
    this.remainAmount,
    this.customerChargeFee,
    this.paymentMethod,
    this.timezoneOrderTime,
    this.orderTime,
    this.descriptor,
    this.merchantCustomerId,
    this.refundList,
    this.orgTransInfo,
  });

  factory TransactionDetailModel.fromJson(Map<String, dynamic> json) => _$TransactionDetailModelFromJson(json);
  Map<String, dynamic> toJson() => _$TransactionDetailModelToJson(this);
}

/// 原交易详情
@JsonSerializable()
class TransactionOriginalTransInfo {
  /// 原交易订单号
  final String? originalTransactionId;

  /// 原交易币种
  final String? originalOrderCurrency;

  /// 原交易金额
  final num? originalOrderAmount;

  /// 原交易订单时间
  final String? originalOrderTime;

  TransactionOriginalTransInfo({
    this.originalTransactionId,
    this.originalOrderCurrency,
    this.originalOrderAmount,
    this.originalOrderTime,
  });

  factory TransactionOriginalTransInfo.fromJson(Map<String, dynamic> json) => _$TransactionOriginalTransInfoFromJson(json);
  Map<String, dynamic> toJson() => _$TransactionOriginalTransInfoToJson(this);
}

/// 退款信息
@JsonSerializable()
class TransactionRefundInfo {
  /// 退款订单号
  final String? transactionId;

  /// 退款币种
  final String? orderCurrency;

  /// 退款金额
  final num? orderAmount;

  /// 退款订单时间
  final String? orderTime;

  TransactionRefundInfo({
    this.transactionId,
    this.orderCurrency,
    this.orderAmount,
    this.orderTime,
  });

  factory TransactionRefundInfo.fromJson(Map<String, dynamic> json) => _$TransactionRefundInfoFromJson(json);
  Map<String, dynamic> toJson() => _$TransactionRefundInfoToJson(this);
}

