// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TransactionDetailModel _$TransactionDetailModelFromJson(
        Map<String, dynamic> json) =>
    TransactionDetailModel(
      clientName: json['clientName'] as String?,
      transactionId: json['transactionId'] as String?,
      authTraceNo: json['authTraceNo'] as String?,
      transactionResult: json['transactionResult'] as String?,
      transactionType: json['transactionType'] as String?,
      orderCurrency: json['orderCurrency'] as String?,
      orderAmount: json['orderAmount'] as num?,
      paymentAmount: json['paymentAmount'] as num?,
      feeAmount: json['feeAmount'] as num?,
      settleAmount: json['settleAmount'] as num?,
      remainAmount: json['remainAmount'] as num?,
      customerChargeFee: json['customerChargeFee'] as num?,
      paymentMethod: json['paymentMethod'] as String?,
      timezoneOrderTime: json['timezoneOrderTime'] as String?,
      orderTime: json['orderTime'] as String?,
      descriptor: json['descriptor'] as String?,
      merchantCustomerId: json['merchantCustomerId'] as String?,
      refundList: (json['refundList'] as List<dynamic>?)
          ?.map(
              (e) => TransactionRefundInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      orgTransInfo: json['orgTransInfo'] == null
          ? null
          : TransactionOriginalTransInfo.fromJson(
              json['orgTransInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TransactionDetailModelToJson(
        TransactionDetailModel instance) =>
    <String, dynamic>{
      'clientName': instance.clientName,
      'transactionId': instance.transactionId,
      'authTraceNo': instance.authTraceNo,
      'transactionResult': instance.transactionResult,
      'transactionType': instance.transactionType,
      'orderCurrency': instance.orderCurrency,
      'orderAmount': instance.orderAmount,
      'paymentAmount': instance.paymentAmount,
      'feeAmount': instance.feeAmount,
      'settleAmount': instance.settleAmount,
      'remainAmount': instance.remainAmount,
      'customerChargeFee': instance.customerChargeFee,
      'paymentMethod': instance.paymentMethod,
      'timezoneOrderTime': instance.timezoneOrderTime,
      'orderTime': instance.orderTime,
      'descriptor': instance.descriptor,
      'merchantCustomerId': instance.merchantCustomerId,
      'refundList': instance.refundList,
      'orgTransInfo': instance.orgTransInfo,
    };

TransactionOriginalTransInfo _$TransactionOriginalTransInfoFromJson(
        Map<String, dynamic> json) =>
    TransactionOriginalTransInfo(
      originalTransactionId: json['originalTransactionId'] as String?,
      originalOrderCurrency: json['originalOrderCurrency'] as String?,
      originalOrderAmount: json['originalOrderAmount'] as num?,
      originalOrderTime: json['originalOrderTime'] as String?,
    );

Map<String, dynamic> _$TransactionOriginalTransInfoToJson(
        TransactionOriginalTransInfo instance) =>
    <String, dynamic>{
      'originalTransactionId': instance.originalTransactionId,
      'originalOrderCurrency': instance.originalOrderCurrency,
      'originalOrderAmount': instance.originalOrderAmount,
      'originalOrderTime': instance.originalOrderTime,
    };

TransactionRefundInfo _$TransactionRefundInfoFromJson(
        Map<String, dynamic> json) =>
    TransactionRefundInfo(
      transactionId: json['transactionId'] as String?,
      orderCurrency: json['orderCurrency'] as String?,
      orderAmount: json['orderAmount'] as num?,
      orderTime: json['orderTime'] as String?,
    );

Map<String, dynamic> _$TransactionRefundInfoToJson(
        TransactionRefundInfo instance) =>
    <String, dynamic>{
      'transactionId': instance.transactionId,
      'orderCurrency': instance.orderCurrency,
      'orderAmount': instance.orderAmount,
      'orderTime': instance.orderTime,
    };
