/*
 * ProjectName：UaApp
 * FilePath：lib/pages/transaction/detail
 * FileName：transaction_detail_page
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/22 13:51:24
 */

import 'package:auapp/main.dart';
import 'package:auapp/pages/login/widgets/login_app_bar.dart';
import 'package:auapp/pages/transaction/detail/widgets/transaction_detail_item_cell.dart';
import 'package:auapp/pub/constants/color_constants.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/widgets/hp_app_bar.dart';
import 'package:auapp/widgets/hp_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:auapp/pages/transaction/detail/transaction_detail_controller.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../r.dart';

class TransactionDetailPage extends GetView<TransactionDetailController> {
  const TransactionDetailPage({super.key});
  @override
  Widget build(BuildContext context) {
    // 获取 AppBar 的默认高度
    final appBarHeight = AppBar().preferredSize.height;
    // 获取状态栏高度（如果需要适配全屏）
    final statusBarHeight = MediaQuery.of(context).padding.top;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.white,
      appBar: HPAppBar(
        text: 'transaction_detail_title'.tr,
        actions: [
          Visibility(
            visible: true,
            child: Container(
              padding: EdgeInsets.only(right: 24.sp),
              child: HPTextButton(
                onPressed: () {
                  /// 点击退款
                  HPRouter.pushPage(Routes.transactionRefund, parameters: {
                    "transactionId": controller
                            .transactionDetailModel.value?.transactionId ??
                        "",
                    "remainAmount": (controller
                                .transactionDetailModel.value?.remainAmount ??
                            0.0)
                        .toString(),
                    "orderCurrency": controller
                            .transactionDetailModel.value?.orderCurrency ??
                        "",
                    "orderAmount":
                        (controller.transactionDetailModel.value?.orderAmount ??
                                0.0)
                            .toString(),
                  });
                },
                child: Text(
                  "transaction_detail_refund".tr,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: ColorConstants.mainThemeColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          Positioned(
            left: 0,
            top: 0,
            width: 1.sw,
            height: 192.h,
            child: Image.asset(
              R.assetsImagesTransactionBackgroundImg,
              fit: BoxFit.cover,
            ),
          ),
          Positioned.fill(
            top: (appBarHeight + statusBarHeight),
            child: Obx(
              () => SmartRefresher(
                controller: controller.refreshController,
                scrollController: controller.scrollController.value,
                onRefresh: () {
                  controller.refreshData();
                },
                onLoading: () {},
                child: ListView(
                  shrinkWrap: true,
                  physics: const ClampingScrollPhysics(),
                  children: [
                    _buildAmountSection(),
                    _buildTransactionDetails(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 拆分金额展示部分
  Widget _buildAmountSection() => Column(
        children: [
          SizedBox(height: 28.sp),
          Obx(() => Text(
                (controller.transactionDetailModel.value?.orderAmount ?? 0.00)
                    .toString(),
                style: TextStyle(
                  fontFamily: "DingTalk-JinBuTi",
                  fontSize: 28.sp,
                ),
              )),
          SizedBox(height: 8.sp),
          Obx(
            () {
              final transaction = controller.transactionDetailModel.value!;
              return Text(
                '${transaction.transactionType == "SALE" ? 'transaction_detail_order_sale_amt_lb'.tr : 'transaction_detail_order_refund_amt_lb'.tr}'
                '(${transaction.orderCurrency})',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xFF6B7275),
                ),
              );
            },
          ),
          SizedBox(height: 34.sp),
        ],
      );

  // 拆分交易明细部分
  Widget _buildTransactionDetails() => Obx(
        () => Column(
          children: [
            ...controller.transactionContentsList,
          ],
        ),
      );
}
