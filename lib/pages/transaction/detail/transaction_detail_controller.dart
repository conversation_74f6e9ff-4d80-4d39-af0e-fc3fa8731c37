/*
 * ProjectName：UaApp
 * FilePath：lib/pages/transaction/detail
 * FileName：transaction_detail_controller
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/22 13:51:44
 */
import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/pages/transaction/detail/models/transaction_detail_model.dart';
import 'package:auapp/pages/transaction/detail/transaction_detail_provider.dart';
import 'package:auapp/pages/transaction/detail/widgets/transaction_detail_item_cell.dart';
import 'package:auapp/routes/app_routes.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class TransactionDetailController extends GetxController {
  final TransactionDetailProvider transactionDetailProvider;
  TransactionDetailController({required this.transactionDetailProvider});

  final Rx<TransactionDetailModel?> transactionDetailModel =
      Rx(TransactionDetailModel());
  final RxList<Widget> transactionContentsList = <Widget>[].obs;
  final RefreshController refreshController = RefreshController();
  final Rx<ScrollController> scrollController = ScrollController().obs;
  final RxList<TransactionRefundInfo>? refundList =
      <TransactionRefundInfo>[].obs;

  late String transactionId;

  @override
  void onInit() {
    super.onInit();

    transactionId = Get.parameters['transactionId']!;
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
    mockData();
  }

  Future<void> mockData() async {
    // Map<String, dynamic> map = {
    //   "clientName": "商户名称",
    //   "transactionId": transactionId,
    //   "authTraceNo": "20250427190741010008C0008705298",
    //   "transactionResult": "SUCCEED",
    //   "transactionType": "SALE",
    //   "orderCurrency": "AUD",
    //   "orderAmount": 100.00,
    //   "paymentAmount": 101.60,
    //   "feeAmount": -25.00,
    //   "remainAmount": 23.98,
    //   "customerChargeFee": 1.60,
    //   "paymentMethod": "Wechat Pay",
    //   "timezoneOrderTime": "04/05 00:00:00",
    //   "orderTime": "04/05 00:00:00 UTC+8",
    //   "descriptor": "描述：正向交易，收款到账",
    //   "merchantCustomerId": "1020250427000001",
    //   "goodsName": "112025042700000003",
    //   "orgTransInfo": {
    //     "originalTransactionId": "1020250427000007",
    //     "originalOrderCurrency": "AUD",
    //     "originalOrderAmount": 100.00,
    //     "originalOrderTime": "04/05 00:00:00 UTC+8",
    //   },
    //   "refundList": [
    //     {
    //       "refundTransactionId": "1020250427000008",
    //       "refundOrderCurrency": "AUD",
    //       "refundOrderAmount": 20.98,
    //       "refundOrderTime": "04/05 00:00:00 UTC+8",
    //     },
    //     {
    //       "refundTransactionId": "1020250427000009",
    //       "refundOrderCurrency": "AUD",
    //       "refundOrderAmount": 20.98,
    //       "refundOrderTime": "04/05 00:00:00 UTC+8",
    //     },
    //   ],
    // };
    // transactionDetailModel.value = TransactionDetailModel.fromJson(map);
    // _buildDynamicContents();

    HPResponse response = await transactionDetailProvider.fetchTransactionDetail(transactionId);
    if (response.isSuccess) {
      transactionDetailModel.value = TransactionDetailModel.fromJson(response.data);
      _buildDynamicContents();
    }
  }

  void _buildDynamicContents() {
    transactionContentsList.clear();

    final model = transactionDetailModel.value!; // 确保非空校验
    final refundList = model.refundList ?? [];

    /// 交易类型
    transactionContentsList.add(
      TransactionDetailItemCell(
        leftTitle: 'transaction_type_lb'.tr,
        rightContent: _getTransactionTypeText(model.transactionType ?? ""),
        rightContentColor: _getTypeColor(model.transactionType ?? ""),
      ),
    );

    /// 交易状态
    transactionContentsList.add(
      TransactionDetailItemCell(
        leftTitle: 'transaction_order_status_lb'.tr,
        rightContent: getOrderStatus(model.transactionResult),
      ),
    );

    /// 服务费（区分正反向交易）
    transactionContentsList.add(
      TransactionDetailItemCell(
        leftTitle: model.transactionType == "SALE"
            ? 'transaction_service_fee_sale_lb'.tr
            : 'transaction_service_fee_refund_lb'.tr,
        rightContent: "${model.orderCurrency} ${model.feeAmount.toString()}",
      ),
    );

    /// 客户承担服务费（仅正向交易）
    if (model.transactionType == "SALE") {
      transactionContentsList.add(
        TransactionDetailItemCell(
          leftTitle: 'transaction_customer_bear_fee_lb'.tr,
          rightContent: "${model.orderCurrency} ${model.customerChargeFee.toString()}",
        ),
      );
    }

    if (model.transactionType == "SALE") {
      transactionContentsList.add(
        TransactionDetailItemCell(
          leftTitle: 'transaction_settled_amt_lb'.tr,
          rightContent: "${model.orderCurrency} ${model.settleAmount.toString()}",
        ),
      );
    }

    /// 商户名称
    transactionContentsList.add(
      TransactionDetailItemCell(
        leftTitle: 'transaction_detail_merchant_name_lb'.tr,
        rightContent: model.clientName,
      ),
    );

    /// 分割线
    transactionContentsList.add(
      Container(
        padding: EdgeInsets.symmetric(vertical: 10.sp),
        child: Divider(
          color: const Color(0xFFE5E6EB),
          height: 0.5.sp,
        ),
      ),
    );

    /// 付款方式
    transactionContentsList.add(
      TransactionDetailItemCell(
        leftTitle: 'transaction_detail_payment_method_lb'.tr,
        rightContent: model.paymentMethod,
      ),
    );

    /// 交易时间(当地时间)
    transactionContentsList.add(
      TransactionDetailItemCell(
        leftTitle: 'transaction_date_local_time_lb'.tr,
        rightContent: model.timezoneOrderTime,
      ),
    );

    /// 交易时间(北京时间)
    transactionContentsList.add(
      TransactionDetailItemCell(
        leftTitle: 'transaction_date_cn_time_lb'.tr,
        rightContent: model.orderTime,
      ),
    );

    /// 客户ID
    transactionContentsList.add(
      TransactionDetailItemCell(
        leftTitle: 'transaction_customer_id_lb'.tr,
        rightContent: model.merchantCustomerId,
      ),
    );

    /// HuePay订单ID
    transactionContentsList.add(
      TransactionDetailItemCell(
        leftTitle: 'transaction_huepay_order_id_lb'.tr,
        rightContent: model.transactionId,
        enableCopy: true,
      ),
    );

    /// 对外交易ID
    transactionContentsList.add(
      TransactionDetailItemCell(
        leftTitle: 'transaction_external_id_lb'.tr,
        rightContent: model.authTraceNo,
        enableCopy: true,
      ),
    );

    /// 备注
    transactionContentsList.add(
      TransactionDetailItemCell(
        leftTitle: 'transaction_detail_remark_lb'.tr,
        rightContent: model.descriptor,
      ),
    );

    /// 圆角
    transactionContentsList.add(
      Container(
        color: ThemeConfig.grayMainBackgroundColor, // 背景颜色
        child: Container(
          height: 18.sp,
          decoration: BoxDecoration(
            color: Colors.white, // 背景颜色
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(16.sp), // 左下圆角
              bottomRight: Radius.circular(16.sp), // 右下圆角
            ),
          ),
        ),
      ),
    );

    /// 分割线
    transactionContentsList.add(
      Container(
        color: ThemeConfig.grayMainBackgroundColor, // 背景颜色
        height: 8.sp,
      ),
    );

    /// 正向交易判断是否有退款列表
    if (model.transactionType == "SALE" &&
        model.refundList != null &&
        model.refundList!.isNotEmpty) {
      /// 圆角
      transactionContentsList.add(
        Container(
          color: ThemeConfig.grayMainBackgroundColor, // 背景颜色
          child: Column(
            mainAxisSize: MainAxisSize.min, //
            children: [
              Container(
                padding: EdgeInsets.only(
                  top: 24.sp,
                  bottom: 10.sp,
                  left: 24.sp,
                  right: 24.sp,
                ),
                width: ScreenUtil().screenWidth,
                decoration: BoxDecoration(
                  color: Colors.white, // 背景颜色
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16.sp), // 左下圆角
                    topRight: Radius.circular(16.sp), // 右下圆角
                  ),
                ),
                child: Text(
                  "transaction_detail_refunds_list_lb".tr,
                  style: TextStyle(
                    color: ThemeConfig.darkTextColor,
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Container(
                color: Colors.white, // 背景颜色
                child: _buildRefundList(refundList),
              ),
            ],
          ),
        ),
      );
    }

    /// 反向交易判断是否有原交易信息
    if (model.transactionType == "REFUND" && model.orgTransInfo != null) {
      /// 圆角
      transactionContentsList.add(
        Container(
          color: ThemeConfig.grayMainBackgroundColor, // 背景颜色
          width: 1.sw,
          child: Container(
            padding: EdgeInsets.all(24.sp),
            decoration: BoxDecoration(
              color: Colors.white, // 背景颜色
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.sp), // 左下圆角
                topRight: Radius.circular(16.sp), // 右下圆角
              ),
            ),
            child: Text(
              "transaction_detail_origin_order_lb".tr,
              style: TextStyle(
                color: const Color(0xFF999999),
                fontSize: 12.sp,
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
        ),
      );

      /// 原交易信息
      transactionContentsList.add(
        TransactionDetailItemCell(
          leftTitle: model.orgTransInfo?.originalOrderTime ?? "-",
          rightContent: model.orgTransInfo?.originalOrderCurrency ??
              "-" + " " + model.orgTransInfo!.originalOrderAmount.toString() ??
              "-",
          showDetail: true,
        ),
      );
    }

    /// 原交易信息
    transactionContentsList.add(
      SizedBox(
        height: 40.sp,
      ),
    );
  }

  // 修改后的退款列表构建方法
  Widget _buildRefundList(List<TransactionRefundInfo> refundList) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: refundList.length,
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) {
        final item = refundList[index];
        return Container(
          color: Colors.white,
          child: TransactionDetailItemCell(
            leftTitle: item.orderTime ?? "-",
            // 修复字符串拼接逻辑
            rightContent:
            "${item.orderCurrency ?? "-"} ${item.orderAmount?.toStringAsFixed(2) ?? "-"}",
            showDetail: true,
            onClickDetail: () {
              /// 原交易跳转到退款详情
              if (transactionDetailModel.value?.transactionType == "SALE") {
                /// 跳转到退款详情
                HPRouter.pushPage(
                  "/transactionDetail/${item.transactionId}",
                );
              } else {
                HPRouter.pushPage(
                  "/transactionDetail/sale/${transactionDetailModel.value?.transactionId}",
                );
              }
            },
          ),
        );
      },
    );
  }

  getOrderStatus(String? transactionResult) {
    String orderStatusDesc = "-";
    switch (transactionResult) {
      case "SUCCEED":
        orderStatusDesc = "transaction_order_status_success".tr;
        break;
      case "PENDING":
        orderStatusDesc = "transaction_order_status_pending".tr;
        break;
      case "FAILED":
        orderStatusDesc = "transaction_order_status_failed".tr;
        break;
      default:
        orderStatusDesc = "-";
        break;
    }
    return orderStatusDesc;
  }

  /// 获取交易类型
  String _getTransactionTypeText(String type) {
    return type == "SALE"
        ? "transaction_type_sale".tr
        : "transaction_type_refund".tr;
  }

  /// 获取颜色
  Color _getTypeColor(String type) {
    return type == "SALE" ? const Color(0xFF38BA13) : const Color(0xFFFD3627);
  }

  Future<void> refreshData() async {
    try {
      // 执行数据刷新逻辑
      await Future.delayed(const Duration(seconds: 2));
      refreshController.refreshCompleted();
    } catch (e) {
      refreshController.refreshFailed();
    }
  }

  @override
  void onClose() {
    super.onClose();
  }

  @override
  dispose() {
    refreshController.dispose(); // 确保销毁
    scrollController.value.dispose();
    Get.delete<TransactionDetailController>();
    super.dispose();
  }
}
