/*
 * ProjectName：UaApp
 * FilePath：lib/pages/transaction/detail
 * FileName：transaction_detail_binding
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/22 13:51:58
 */

import 'package:auapp/base/base_binding.dart';
import 'package:auapp/pages/transaction/detail/transaction_detail_controller.dart';
import 'package:auapp/pages/transaction/detail/transaction_detail_provider.dart';
import 'package:get/get.dart';

class TransactionDetailBinding implements BaseBinding {
  @override
  void dependencies() {
    Get.lazyPut<TransactionDetailController>(() => TransactionDetailController(transactionDetailProvider: Get.find()));
    Get.lazyPut(() => TransactionDetailProvider());
  }
}
