/*
 * ProjectName：UaApp
 * FilePath：lib/pages/transaction/detail/widgets
 * FileName：transaction_detail_item_cell
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/23 10:39:24
 */

import 'package:auapp/pub/constants/color_constants.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TransactionDetailItemCell extends StatelessWidget {
  final String leftTitle;
  final String? rightContent;
  final Color? rightContentColor;
  final bool? enableCopy;
  final bool? showDetail;
  final VoidCallback? onClickDetail;
  const TransactionDetailItemCell({
    super.key,
    required this.leftTitle,
    this.rightContent,
    this.rightContentColor,
    this.enableCopy = false,
    this.showDetail = false,
    this.onClickDetail,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 6.sp, horizontal: 24.sp),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Container(
              alignment: Alignment.topLeft,
              child: Text(
                leftTitle,
                softWrap: true, // 启用换行
                style: TextStyle(
                  fontSize: 13.0.sp,
                  color: const Color(0xFF6B7275),
                ),
              ),
            ),
          ),
          SizedBox(width: 10.sp), //
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Container(
                    alignment: Alignment.topRight,
                    child: Text(
                      rightContent ?? "-",
                      softWrap: true, // 启用换行
                      style: TextStyle(
                        fontSize: 13.0.sp,
                        color:
                            rightContentColor ?? ColorConstants.darkTextColor,
                      ),
                    ),
                  ),
                ),
                Visibility(
                  visible: enableCopy ?? false,
                  child: GestureDetector(
                    onTap: () async {
                      if (!TextUtil.isEmpty(rightContent)) {
                        await Clipboard.setData(
                          ClipboardData(text: rightContent ?? ""),
                        );
                        HPLoading.showToast('transaction_copy_success_tips'.tr,);
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.only(left: 4.sp),
                      child: Text(
                        'transaction_content_copy_btn'.tr,
                        style: TextStyle(
                          fontSize: 13.0.sp,
                          color: ColorConstants.mainThemeColor,
                        ),
                      ),
                    ),
                  ),
                ),
                Visibility(
                  visible: showDetail ?? false,
                  child: GestureDetector(
                    onTap: () async {
                      if (onClickDetail != null) {
                        onClickDetail!();
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.only(left: 4.sp),
                      child: Text(
                        'transaction_check_order_detail_btn'.tr,
                        style: TextStyle(
                          fontSize: 13.0.sp,
                          color: ColorConstants.mainThemeColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
