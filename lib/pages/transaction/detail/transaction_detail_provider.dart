/*
 * ProjectName：UaApp
 * FilePath：lib/pages/transaction/detail
 * FileName：transaction_detail_provider
 * Copyright © 2025 PnR Data Service Co.,Ltd. All rights reserved.
 *
 * <AUTHOR>
 * @description 
 * @date 2025/5/22 13:52:26
 */

import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';

class TransactionDetailProvider {
  /// 交易详情接口
  Future<HPResponse> fetchTransactionDetail(String transactionId) async {
    HPResponse response = await HttpRequest.post(
      ApiConstants.transactionDetailInfoUrl,
      customBaseUrl: ApiConstants.baseTransactionUrl,
      params: {
        "transactionId": transactionId,
      }
    );
    return response;
  }
}
