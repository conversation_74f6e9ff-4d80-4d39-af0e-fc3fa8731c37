/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-16 15:25:45
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-23 17:06:57
 * @FilePath: /hwicc-mobile-flutter/lib/pages/transaction/transaction_main_controller.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:auapp/api/api_constants.dart';
import 'package:auapp/api/http_request.dart';
import 'package:auapp/api/model/hp_response.dart';
import 'package:auapp/base/base_controller.dart';
import 'package:auapp/pages/transaction/model/transaction_model.dart';
import 'package:auapp/utils/hp_date_util.dart';
import 'package:auapp/utils/hp_loading.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class TransactionMainController extends BaseController {
  TransactionMainController();

  final RefreshController refreshController = RefreshController();
  final RefreshController dayRefreshController = RefreshController();
  final Rx<ScrollController> scrollController = ScrollController().obs;

  // 时间轴显示的月份，默认当前月
  final RxString currMonth = "".obs;
  final RxString currDate = "".obs;
  final RxString currMonthName = "".obs;
  final RxString currYearName = "".obs;

  // 交易时间
  final RxList<TransactionStatisticDayModel> transactionTimeList =
      <TransactionStatisticDayModel>[].obs;
  // 交易汇总
  final Rx<TransactionInfoSummaryModel> transactionInfoSummaryModel =
      TransactionInfoSummaryModel().obs;
  // 交易列表
  final RxList<TransactionInfoListModel> transactionInfoListModel =
      <TransactionInfoListModel>[].obs;

  // 分页请求参数
  final RxInt pageNum = 1.obs;
  final RxInt total = 0.obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();

    reloadData();
  }

  @override
  void onShow() {
    // TODO: implement onShow
    super.onShow();

    reloadData();
  }

  reloadData() {
    initDateValue();
    requestTransactionDayData(currMonth.value);
    requestTransactionInfoListData(currDate.value);
    requestTransactionInfoSummaryData(currDate.value);
  }

  initDateValue() {
    if (currDate.value.isEmpty) {
      currDate.value =
          HPDateUtil.dateFormat(DateTime.now(), format: 'yyyyMMdd');
    }
    DateTime dateTime = HPDateUtil.dateParse(currDate.value) ?? DateTime.now();

    currMonth.value = HPDateUtil.dateFormat(dateTime, format: 'yyyyMM');
    currMonthName.value = HPDateUtil.getZHMonthDay(dateTime);
    currYearName.value = HPDateUtil.dateFormat(dateTime, format: 'yyyy');
  }

  // 切换日期
  void switchDate(String date) {
    currDate.value = date;

    requestTransactionInfoListData(date);
    requestTransactionInfoSummaryData(date);
  }

  // 判断是否可以切换月份
  // 当前日期为当月不能切下个月。当前月份为2025年1月，不能切换下个月
  bool canSwitchMonth(String switchType) {
    if (switchType == 'prev') {
      return HPDateUtil.stringFormat(currDate.value, format: 'yyyyMM') !=
          "202501";
    }
    DateTime? curr = HPDateUtil.dateParse(currDate.value);
    if (curr == null) {
      return false;
    }
    return curr.month != DateTime.now().month;
  }

  // 切换月份之后将当前时间置为下月的一号，或者上月的最后一天
  // switchType: 'prev' 上个月，'next' 下个月
  void switchMonth(String switchType) {
    DateTime? curr = HPDateUtil.dateParse(currDate.value);
    DateTime newDate = switchType == 'prev'
        ? HPDateUtil.getLastDayOfMonth(HPDateUtil.subtractMonths(curr, 1))
        : HPDateUtil.getFirstDayOfMonth(HPDateUtil.addMonths(curr, 1));
    currDate.value = HPDateUtil.dateFormat(newDate, format: 'yyyyMMdd');
    reloadData();
  }

  // 统计存在交易的日期列表
  requestTransactionDayData(String date) async {
    try {
      HPResponse response = await HttpRequest.post(
          ApiConstants.transactionDayDataUrl,
          customBaseUrl: ApiConstants.baseTransactionUrl,
          params: {"transactionMonth": date});
      if (response.isSuccess) {
        final res = response.data;
        if (res != null && res is List) {
          transactionTimeList.assignAll(
            res.map((e) => TransactionStatisticDayModel.fromJson(e)).toList(),
          );

          // dayRefreshController 要偏移到当前选中的日期
          scrollController.value.animateTo(
            transactionTimeList
                    .indexWhere((element) => element.date == currDate.value) *
                50,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      } else {
        HPLoading.showToast(response.msg);
      }
    } catch (e) {
      HPLoading.showToast(e.toString());
    } finally {
      dayRefreshController.refreshCompleted();
      dayRefreshController.loadComplete();
    }
  }

  // 下拉刷新当前日期的汇总和列表数据
  refreshCurrDateData() async {
    pageNum.value = 1;
    requestTransactionInfoSummaryData(currDate.value);
    await requestTransactionInfoListData(currDate.value);

    refreshController.refreshCompleted();
    refreshController.resetNoData();
  }

  // 上拉加载更多列表数据
  onLoadMoreListData() async {
    await requestTransactionInfoListData(currDate.value);

    if (transactionInfoListModel.length < total.value) {
      refreshController.loadComplete();
    } else {
      refreshController.loadNoData();
    }
  }

  // 根据日期请求具体的交易数据
  requestTransactionInfoListData(String date) async {
    try {
      String dateFormat = HPDateUtil.stringFormat(date, format: 'yyyy-MM-dd');
      HPResponse response = await HttpRequest.post(
          ApiConstants.transactionInfoListUrl,
          customBaseUrl: ApiConstants.baseTransactionUrl,
          params: {
            "beginDateTime": "$dateFormat 00:00:00",
            "endDateTime": "$dateFormat 23:59:59",
            "pageNum": pageNum.value,
            "pageSize": 20,
          });
      if (response.isSuccess) {
        final res = response.data;
        if (res != null && res is List) {
          if (pageNum.value == 1) {
            transactionInfoListModel.assignAll(
              res.map((e) => TransactionInfoListModel.fromJson(e)).toList(),
            );
          } else {
            transactionInfoListModel.addAll(
              res.map((e) => TransactionInfoListModel.fromJson(e)).toList(),
            );
          }
          total.value = response.total != null ? response.total!.toInt() : 0;
          if (res.isNotEmpty && transactionInfoListModel.length < total.value) {
            pageNum.value++;
          }
        }
      } else {
        HPLoading.showToast(response.msg);
      }
    } catch (e) {
      HPLoading.showToast(e.toString());
    }
  }

  // 获取看板的汇总数据
  requestTransactionInfoSummaryData(String date) async {
    try {
      String dateFormat = HPDateUtil.stringFormat(date, format: 'yyyy-MM-dd');
      HPResponse response = await HttpRequest.post(
          ApiConstants.transactionInfoSummaryUrl,
          customBaseUrl: ApiConstants.baseTransactionUrl,
          params: {
            "beginDateTime": "$dateFormat 00:00:00",
            "endDateTime": "$dateFormat 23:59:59",
          });
      if (response.isSuccess) {
        final res = response.data;
        if (res != null) {
          transactionInfoSummaryModel.value =
              TransactionInfoSummaryModel.fromJson(res);
        }
      } else {
        HPLoading.showToast(response.msg);
      }
    } catch (e) {
      HPLoading.showToast(e.toString());
    }
  }
}
