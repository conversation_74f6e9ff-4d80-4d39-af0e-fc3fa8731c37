/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-05-16 15:25:45
 * @LastEditors: huaze.fan <EMAIL>
 * @LastEditTime: 2025-05-23 17:58:03
 * @FilePath: /hwicc-mobile-flutter/lib/pages/transaction/transaction_main_page.dart
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import 'package:auapp/models/HPUserInfoSingleton.dart';
import 'package:auapp/pages/transaction/transaction_main_controller.dart';
import 'package:auapp/pages/transaction/widget/trans_databoard_widget.dart';
import 'package:auapp/pages/transaction/widget/trans_dateaxle_widget.dart';
import 'package:auapp/r.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/widgets/hp_app_bar.dart';
import 'package:auapp/widgets/hp_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TransactionMainPage extends GetView<TransactionMainController> {
  const TransactionMainPage({super.key});

  List<Widget> _navigationBar(BuildContext context) {
    return [
      Positioned(
        left: 0,
        top: 0,
        width: 1.sw,
        height: 192.h,
        child: Image.asset(R.assetsImagesTransactionBackgroundImg,
            fit: BoxFit.cover),
      ),
      Positioned(
        left: 24.w,
        right: 24.w,
        top: MediaQuery.of(context).padding.top + 8,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            HPTextButton(
              onPressed: () {},
              child: Row(
                children: [
                  Container(
                    constraints: BoxConstraints(maxWidth: 1.sw - 120.w),
                    child: Text(
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      HPUserInfoSingleton().custShortName ?? '',
                      style: ThemeConfig.boldBackText18,
                    ),
                  ),
                  // Image.asset(
                  //   R.assetsImagesHomeMerDropdown,
                  //   width: 24.w,
                  //   height: 24.w,
                  // ),
                ],
              ),
            ),
            // HPTextButton(
            //     onPressed: () {},
            //     child: Text(
            //       "transaction_btn_filter".tr,
            //       style: ThemeConfig.norGrayText12.copyWith(
            //           fontSize: 16.sp, color: const Color(0xFF0FACF5)),
            //     ))
          ],
        ),
      )
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: const HPAppBar(
        showLeading: false,
      ),
      body: Stack(
        children: [
          ..._navigationBar(context),
          Positioned(
            top: 100.h,
            left: 0,
            right: 0,
            child: TransDateaxleWidget(),
          ),
          Positioned.fill(
            top: 190.h,
            left: 0,
            right: 0,
            child: TransDataboardWidget(),
          )
        ],
      ),
    );
  }
}
