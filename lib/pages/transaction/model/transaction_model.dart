// 交易日期
class TransactionStatisticDayModel {
  String? date;
  bool? hasData;
  bool isSelect = false;

  TransactionStatisticDayModel({this.date, this.hasData});

  TransactionStatisticDayModel.fromJson(Map<String, dynamic> json) {
    date = json['date'];
    hasData = json['hasData'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['date'] = date;
    data['hasData'] = hasData;
    return data;
  }
}

// 交易汇总
class TransactionInfoSummaryModel {
  num? totalAmount;
  num? totalSuccessOrder;
  num? totalRefundOrder;
  num? totalRefundAmount;

  TransactionInfoSummaryModel({
    this.totalAmount,
    this.totalSuccessOrder,
    this.totalRefundOrder,
    this.totalRefundAmount,
  });

  TransactionInfoSummaryModel.fromJson(Map<String, dynamic> json) {
    totalAmount = json['totalAmount'] ?? 0;
    totalSuccessOrder = json['totalSuccessOrder'] ?? 0;
    totalRefundOrder = json['totalRefundOrder'] ?? 0;
    totalRefundAmount = json['totalRefundAmount'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['totalAmount'] = totalAmount;
    data['totalSuccessOrder'] = totalSuccessOrder;
    data['totalRefundOrder'] = totalRefundOrder;
    data['totalRefundAmount'] = totalRefundAmount;
    return data;
  }
}

// 交易列表
class TransactionInfoListModel {
  String? transactionId;
  String? authTraceNo;
  String? orderTime;
  String? transactionResult;
  String? paymentMethod;
  String? transactionType;
  String? orderCurrency;
  num? orderAmount;
  num? paymentAmount;
  num? feeAmount;

  TransactionInfoListModel(
      {this.transactionId,
      this.authTraceNo,
      this.orderTime,
      this.transactionResult,
      this.paymentMethod,
      this.transactionType,
      this.orderCurrency,
      this.orderAmount,
      this.paymentAmount,
      this.feeAmount});

  TransactionInfoListModel.fromJson(Map<String, dynamic> json) {
    transactionId = json['transactionId'] ?? '';
    authTraceNo = json['authTraceNo'] ?? '';
    orderTime = json['orderTime'] ?? '';
    transactionResult = json['transactionResult'] ?? '';
    paymentMethod = json['paymentMethod'] ?? '';
    transactionType = json['transactionType'] ?? '';
    orderCurrency = json['orderCurrency'] ?? '';
    orderAmount = json['orderAmount'] ?? 0;
    paymentAmount = json['paymentAmount'] ?? 0;
    feeAmount = json['feeAmount'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['transactionId'] = transactionId;
    data['authTraceNo'] = authTraceNo;
    data['orderTime'] = orderTime;
    data['transactionResult'] = transactionResult;
    data['paymentMethod'] = paymentMethod;
    data['transactionType'] = transactionType;
    data['orderCurrency'] = orderCurrency;
    data['orderAmount'] = orderAmount;
    data['paymentAmount'] = paymentAmount;
    data['feeAmount'] = feeAmount;
    return data;
  }
}
