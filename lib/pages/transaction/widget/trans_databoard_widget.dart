import 'package:auapp/pages/transaction/model/transaction_model.dart';
import 'package:auapp/pages/transaction/transaction_main_controller.dart';
import 'package:auapp/r.dart';
import 'package:auapp/routes/hp_router.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/utils/hp_amount_util.dart';
import 'package:auapp/utils/hp_date_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class TransDataboardWidget extends StatelessWidget {
  TransDataboardWidget({super.key});

  final controller = Get.find<TransactionMainController>();

  Widget _databoardWidget() {
    final List<Map<String, dynamic>> dataStrList = [
      {
        "name": "${"transaction_successful_amount".tr}(AUD)",
        "num": HPAmountUtil.formatAbsComma(
            controller.transactionInfoSummaryModel.value.totalAmount),
      },
      {
        "name": "${"transaction_refund_amount".tr}(AUD)",
        "num": HPAmountUtil.formatAbsComma(
            controller.transactionInfoSummaryModel.value.totalRefundAmount,
            absValue: true),
      },
      {
        "name": "transaction_number_successful".tr,
        "num": controller.transactionInfoSummaryModel.value.totalSuccessOrder,
      },
      {
        "name": "transaction_number_refund".tr,
        "num": controller.transactionInfoSummaryModel.value.totalRefundOrder,
      },
    ];

    return Container(
      padding: EdgeInsets.only(top: 8.h, left: 24.w, right: 24.w, bottom: 24.h),
      margin: EdgeInsets.only(bottom: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(16.sp)),
      ),
      child: Wrap(
        children: [
          ...dataStrList.map((e) {
            return Container(
              padding: EdgeInsets.only(top: 16.h),
              width: (1.sw - 48.w) / 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "${e["num"]}",
                    style:
                        ThemeConfig.boldBackAmount18.copyWith(fontSize: 16.sp),
                  ),
                  SizedBox(
                    height: 4.h,
                  ),
                  Text(
                    e["name"],
                    style: ThemeConfig.norGrayText12.copyWith(fontSize: 11.sp),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _listIcon(String payType, String payStat) {
    return Container(
      padding: EdgeInsets.all(7.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.sp),
          color: payType == 'ALIPAYCN'
              ? payStat == 'SALE'
                  ? Color(0xffE3FAFF)
                  : Color(0xffFFEFE3)
              : payStat == 'SALE'
                  ? Color(0xffEAFFE3)
                  : Color(0xffFFEFE3)),
      child: Image.asset(
        payType == 'ALIPAYCN'
            ? payStat == 'SALE'
                ? R.assetsImagesAlipaySaleIcon
                : R.assetsImagesAlipayRefundIcon
            : payStat == 'SALE'
                ? R.assetsImagesWxpaySaleIcon
                : R.assetsImagesWxpayRefundIcon,
        width: 24.w,
        height: 24.w,
      ),
    );
  }

  Widget _listCell(TransactionInfoListModel itemModel) {
    return Container(
      padding: EdgeInsets.only(top: 8.h, left: 24.w, right: 24.w, bottom: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(16.sp)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              _listIcon(itemModel.paymentMethod!, itemModel.transactionType!),
              SizedBox(
                width: 12.w,
              ),
              Text(
                HPDateUtil.stringFormatLocale(
                  itemModel.orderTime,
                ),
                style: ThemeConfig.norGrayText12.copyWith(fontSize: 13.sp),
              ),
            ],
          ),
          Text(
            "${itemModel.transactionType == 'SALE' ? '+' : '-'}${HPAmountUtil.formatAbsComma(itemModel.orderAmount)}",
            style: ThemeConfig.boldBackAmount18.copyWith(fontSize: 13.sp),
          ),
        ],
      ),
    );
  }

  Widget _nodataWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            R.assetsImagesTransactionNodataIcon,
            width: 150.w,
            height: 100.w,
          ),
          Text(
            "transaction_no_transaction_record".tr,
            style: ThemeConfig.norGrayText12.copyWith(fontSize: 13.sp),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => SmartRefresher(
          controller: controller.refreshController,
          enablePullDown: true,
          enablePullUp: true,
          onRefresh: () {
            controller.refreshCurrDateData();
          },
          onLoading: () {
            controller.onLoadMoreListData();
          },
          child: controller.transactionInfoListModel.isEmpty
              ? Column(
                  children: [
                    Obx(() => _databoardWidget()),
                    _nodataWidget(),
                  ],
                )
              : ListView.builder(
                  padding: EdgeInsets.zero,
                  itemCount: controller.transactionInfoListModel.length + 1,
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      return Obx(() => _databoardWidget());
                    }
                    TransactionInfoListModel itemModel =
                        controller.transactionInfoListModel[index - 1];
                    return GestureDetector(
                      onTap: () {
                        print("itemModel: ${itemModel.toJson()}");
                        HPRouter.pushPage(
                            "/transactionDetail/${itemModel.transactionId}");
                      },
                      child: _listCell(itemModel),
                    );
                  },
                ),
        ));
  }
}
