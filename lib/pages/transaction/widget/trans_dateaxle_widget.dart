import 'package:auapp/pages/transaction/model/transaction_model.dart';
import 'package:auapp/pages/transaction/transaction_main_controller.dart';
import 'package:auapp/theme/theme_config.dart';
import 'package:auapp/utils/hp_date_util.dart';
import 'package:auapp/widgets/hp_datetime_picker.dart';
import 'package:auapp/widgets/hp_text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class TransDateaxleWidget extends StatelessWidget {
  TransDateaxleWidget({super.key});

  final controller = Get.find<TransactionMainController>();

  Widget _dateMonthWidget(BuildContext context) {
    return HPTextButton(
      onPressed: () {
        HPDateTimePicker.show(
                context: context,
                dateFormat: 'yyyyMM',
                initialDate: HPDateUtil.dateParse(controller.currDate.value))
            .then((date) {
          if (date != null) {
            controller.reloadData(date.dateTime);
          }
        });
      },
      child: Container(
        width: 89.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.sp),
          color: Colors.white,
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    controller.currYearName.value,
                    style: ThemeConfig.norBackText12.copyWith(fontSize: 13),
                  ),
                  SizedBox(
                    height: 4.h,
                  ),
                  Text(controller.currMonthName.value,
                      style: ThemeConfig.boldBackText18.copyWith(fontSize: 16)),
                ],
              ),
              SizedBox(
                width: 5.w,
              ),
              const Icon(
                Icons.arrow_forward_ios,
                size: 16.0,
                color: Color(0xFF6B7276),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _dateDayWidget(TransactionStatisticDayModel itemModel) {
    String dayStr = itemModel.date != null
        ? HPDateUtil.stringFormat(itemModel.date!, format: 'dd')
        : " ";
    String weekStr = itemModel.date != null
        ? HPDateUtil.getZHWeekDay(HPDateUtil.dateParse(itemModel.date!))
        : " ";
    bool hasData = itemModel.hasData ?? false;

    // 按钮背景色
    Color bgColor = controller.currDate.value == itemModel.date
        ? const Color(0xFF0FACF5)
        : const Color(0xFFFFFFFF);

    Color hasDatabgColor = controller.currDate.value == itemModel.date
        ? const Color(0xFFFFFFFF)
        : const Color(0xFF0FACF5);

    // 文字颜色
    Color textColor = controller.currDate.value == itemModel.date
        ? Colors.white
        : const Color(0xFF222527);

    return HPTextButton(
      onPressed: () {
        if (itemModel.date != null) {
          controller.switchDate(itemModel.date!);
        }
      },
      child: Padding(
        padding: EdgeInsets.only(left: 8.w),
        child: Container(
          width: 42.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.sp),
            color: bgColor,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: 14.h,
              ),
              Text(
                dayStr,
                style: ThemeConfig.boldBackText18
                    .copyWith(fontSize: 13, color: textColor),
              ),
              SizedBox(
                height: 8.h,
              ),
              Text(
                weekStr,
                style: ThemeConfig.norBackText12
                    .copyWith(fontSize: 11, color: textColor),
              ),
              SizedBox(
                height: 18.h,
              ),
              Visibility(
                visible: hasData,
                child: Container(
                  width: 5.w,
                  height: 5.w,
                  decoration: BoxDecoration(
                      color: hasDatabgColor,
                      borderRadius: BorderRadius.circular(5.w)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 1.w,
      height: 82.h,
      child: Row(
        children: [
          Obx(() => _dateMonthWidget(context)),
          Expanded(
              child: Obx(() => SmartRefresher(
                    controller: controller.dayRefreshController,
                    scrollController: controller.scrollController.value,
                    enablePullDown: controller.canSwitchMonth("prev"),
                    enablePullUp: controller.canSwitchMonth("next"),
                    header: const ClassicHeader(
                      idleText: "",
                      releaseText: "",
                      refreshingText: "",
                      idleIcon: Icon(Icons.refresh,
                          size: 16.0, color: Color(0xFF6B7276)),
                      releaseIcon: Icon(Icons.refresh,
                          size: 16.0, color: Color(0xFF6B7276)),
                      refreshingIcon: Icon(Icons.refresh,
                          size: 16.0, color: Color(0xFF6B7276)),
                    ),
                    footer: const ClassicFooter(
                      idleText: "",
                      loadingText: "",
                      noDataText: "",
                      failedText: "",
                      canLoadingText: "",
                      idleIcon: Icon(Icons.refresh,
                          size: 16.0, color: Color(0xFF6B7276)),
                      canLoadingIcon: Icon(Icons.refresh,
                          size: 16.0, color: Color(0xFF6B7276)),
                    ),
                    onRefresh: () {
                      controller.switchMonth("prev");
                    },
                    onLoading: () {
                      controller.switchMonth("next");
                    },
                    child: ListView.builder(
                        padding: EdgeInsets.only(left: 0.w, right: 10.w),
                        scrollDirection: Axis.horizontal,
                        itemCount: controller.transactionTimeList.length,
                        itemBuilder: (context, index) {
                          return Obx(() => _dateDayWidget(
                              controller.transactionTimeList[index]));
                        }),
                  )))
        ],
      ),
    );
  }
}
