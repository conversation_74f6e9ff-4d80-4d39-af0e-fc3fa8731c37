name: auapp
description: 'Huepay project.'
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # aliyun push
#  aliyun_push: 0.1.7

  # GetX
  get: 4.6.6

  # 网络请求
  dio: 4.0.6

  # 用于标记 Model 类
  json_annotation: ^4.9.0

  # 加载 loading
  flutter_easyloading: 3.0.5

  # 工具类
  common_utils: ^2.1.0
  intl: 0.19.0

  # UI 绘制
  flutter_screenutil: ^5.9.3

  # 设备信息
  device_info_plus: 9.1.2

  # 包信息
  package_info_plus: 8.3.0

  # 权限管理
  #  permission_handler: ^12.0.0+1

  # SP 存储
  shared_preferences: ^2.5.3

  # 加密
  crypto: ^3.0.6

  # 加载图片
  cached_network_image: 3.4.1

  # 极验
  gt3_flutter_plugin: 0.1.1

  # FCM推送
  #  firebase_messaging: 14.7.10
  #  flutter_local_notifications: ^19.2.0
  # Firebase
  #  firebase_core: 2.27.1
  # radio 组件
  #  group_button: ^5.3.4

  # 列表
  pull_to_refresh: ^2.0.0

  # 日期选择器
  flutter_datetime_picker_plus: ^2.2.0

  # webview
  webview_flutter: 4.10.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  aliyun_push:
    git:
      url: ********************:tibin.zhang/hf_aliyun_push.git

dev_dependencies:
  flutter_test:
    sdk: flutter

  # 代码生成工具
  build_runner: 2.4.13

  # 自动生成 JSON 序列化代码
  json_serializable: 6.9.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    # assets-generator-begin
    # assets/images/*
    - assets/images/login_region_arraw_down.webp
    - assets/images/tab_reports_sel.webp
    - assets/images/mine_bank_icon.webp
    - assets/images/setting_alert_icon.webp
    - assets/images/tab_home_unsel.webp
    - assets/images/app_launcher.webp
    - assets/images/login_region_choose.webp
    - assets/images/mine_background_icon.webp
    - assets/images/mine_set_icon.webp
    - assets/images/mine_settle_icon.webp
    - assets/images/wxpay_refund_icon.webp
    - assets/images/login_icon_mobile.webp
    - assets/images/wxpay_sale_icon.webp
    - assets/images/setting_close_account_success.png
    - assets/images/tab_transaction_sel.webp
    - assets/images/home_func_account.webp
    - assets/images/mine_help_icon.webp
    - assets/images/home_background_img.webp
    - assets/images/login_region_china.webp
    - assets/images/tab_transaction_unsel.webp
    - assets/images/login_arraw_down.webp
    - assets/images/mine_receipt_icon.webp
    - assets/images/home_func_settle.webp
    - assets/images/login_region_australia.webp
    - assets/images/setting_close_account_alert_icon.webp
    - assets/images/home_background_mer_img.webp
    - assets/images/tab_mine_unsel.webp
    - assets/images/login_pwd_cipher.webp
    - assets/images/select_merchant_bg.webp
    - assets/images/tab_reports_unsel.webp
    - assets/images/transaction_background_img.webp
    - assets/images/app_icon.webp
    - assets/images/alipay_refund_icon.webp
    - assets/images/alipay_sale_icon.webp
    - assets/images/home_mer_dropdown.webp
    - assets/images/mine_merinfo_icon.webp
    - assets/images/login_popup_close.webp
    - assets/images/login_pwd_plain.webp
    - assets/images/tab_home_sel.webp
    - assets/images/setting_close_account_top_bg.webp
    - assets/images/login_choose_box_sel.webp
    - assets/images/login_choose_box_unsel.webp
    - assets/images/login_icon_del.webp
    - assets/images/login_icon_email.webp
    - assets/images/transaction_nodata_icon.webp
    - assets/images/tab_mine_sel.webp
    # assets-generator-end

    - assets/mock/login_response.json
    - assets/mock/login_region_code.json
  #    - assets/img/
  #   - images/a_dot_ham.jpeg
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
  fonts:
    - family: DingTalk-JinBuTi
      fonts:
        - asset: assets/fonts/DingTalk-JinBuTi.ttf
