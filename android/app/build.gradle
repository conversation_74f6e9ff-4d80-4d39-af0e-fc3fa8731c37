plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def releaseTime() {
    return new Date().format("yyyyMMdd", TimeZone.getTimeZone("UTC"))
}
android {
    namespace = "com.huepay.auapp"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

//    buildFeatures {
//        buildConfig = true
//    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.huepay.auapp"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = JENKINS_BUILD_NUMBER as int
        versionName = flutter.versionName
        
//        manifestPlaceholders = [
//            ALI_APP_KEY: keystoreProperties['aliAppKeyDebug'],
//            ALI_APP_SECRET: keystoreProperties['aliAppSecretDebug']
//        ]
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
            enableV1Signing = true
            enableV2Signing = true
        }
    }

    buildTypes {
        debug {
            // TODO: Add your own signing config for the debug build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.release
            manifestPlaceholders = [
                    ALI_APP_KEY: keystoreProperties['aliAppKeyDebug'],
                    ALI_APP_SECRET: keystoreProperties['aliAppSecretDebug'],
            ]
        }
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            minifyEnabled false
            shrinkResources false
            signingConfig = signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            manifestPlaceholders = [
                    ALI_APP_KEY: keystoreProperties['aliAppKeyRelease'],
                    ALI_APP_SECRET: keystoreProperties['aliAppSecretRelease'],
            ]
        }
    }

        applicationVariants.all { variant ->
            variant.outputs.all {
                if (variant.buildType.name.equals('release')) { // 发布
                    def fileName = "HuePay_${releaseTime()}_v${defaultConfig.versionName}_build${defaultConfig.versionCode}_release.apk"
                    outputFileName = fileName;
                } else if (variant.buildType.name.equals('debug')) {    // 测试
                    def fileName = "HuePay_${releaseTime()}_v${defaultConfig.versionName}_build${defaultConfig.versionCode}_test.apk"
                    outputFileName = fileName;
                }
            }
        }

    dependencies {
        implementation 'com.aliyun.ams:alicloud-android-third-push:3.9.4.1'
        implementation 'com.aliyun.ams:alicloud-android-third-push-fcm:3.9.4.1'
    }
}

flutter {
    source = "../.."
}
