PODS:
  - Ali<PERSON>loudELS (1.0.1)
  - Ali<PERSON>loudPush (3.1.1):
    - AlicloudELS (< 2.0, >= 1.0.1)
    - AlicloudUTDID (~> 1.0)
  - AlicloudUTDID (1.6.1)
  - ali<PERSON>_push (0.0.1):
    - Ali<PERSON>loudPush
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - gt3_flutter_plugin (0.0.9):
    - Flutter
    - GT3Captcha-iOS
  - GT3Captcha-iOS (0.15.9)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - ali<PERSON>_push (from `.symlinks/plugins/aliyun_push/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - gt3_flutter_plugin (from `.symlinks/plugins/gt3_flutter_plugin/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  https://github.com/aliyun/aliyun-specs.git:
    - AlicloudELS
    - AlicloudPush
    - AlicloudUTDID
  https://github.com/CocoaPods/Specs.git:
    - GT3Captcha-iOS

EXTERNAL SOURCES:
  aliyun_push:
    :path: ".symlinks/plugins/aliyun_push/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  gt3_flutter_plugin:
    :path: ".symlinks/plugins/gt3_flutter_plugin/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  AlicloudELS: 4d0a5317cc96dad1dd251a35698230ce0dda89dc
  AlicloudPush: edf97ae6886c656586d572db7d3b703347b6faa9
  AlicloudUTDID: 5d2f22d50e11eecd38f30bc7a48c71925ea90976
  aliyun_push: e92c2a8ca91b134bfabf7631816cce5f36e04a44
  device_info_plus: c6fb39579d0f423935b0c9ce7ee2f44b71b9fce6
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  gt3_flutter_plugin: 5bd2c08d3c19cbb6ee3b08f4358439e54c8ab2ee
  GT3Captcha-iOS: aeb6fed2e8594099821430a89208679e5a55b740
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188

PODFILE CHECKSUM: 33ca35fd7ae83597cf10509a387edc8064dc6034

COCOAPODS: 1.15.2
